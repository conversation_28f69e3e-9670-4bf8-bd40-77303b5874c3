const mongoose = require('mongoose');
const Announcement = require('../models/Announcement');

// Hàm tạo title từ content
function generateTitleFromContent(content) {
  if (!content) return 'Thông báo';
  const cleanContent = content.replace(/<[^>]*>/g, '');
  let title = cleanContent.substring(0, 50).trim();
  const lastPunctuation = Math.max(
    title.lastIndexOf('.'),
    title.lastIndexOf('!'),
    title.lastIndexOf('?'),
    title.lastIndexOf(':')
  );
  if (lastPunctuation > 20) {
    title = title.substring(0, lastPunctuation + 1);
  } else if (title.length === 50 && cleanContent.length > 50) {
    const lastSpace = title.lastIndexOf(' ');
    if (lastSpace > 20) {
      title = title.substring(0, lastSpace) + '...';
    } else {
      title = title + '...';
    }
  }
  return title;
}

async function migrateAnnouncementTitles() {
  try {
    console.log('🚀 Bắt đầu migration thêm title cho thông báo...');
    
    // Kết nối MongoDB trực tiếp
    const uri = 'mongodb://localhost:27017/school_management';
    console.log('MONGO_URI:', uri);
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      family: 4
    });
    console.log('✅ Đã kết nối database');
    
    const announcementsWithoutTitle = await Announcement.find({
      $or: [
        { title: { $exists: false } },
        { title: '' },
        { title: null }
      ]
    });
    
    console.log(`📊 Tìm thấy ${announcementsWithoutTitle.length} thông báo cần thêm title`);
    
    if (announcementsWithoutTitle.length === 0) {
      console.log('✅ Tất cả thông báo đã có title. Không cần migration.');
      return;
    }
    
    let updatedCount = 0;
    let errorCount = 0;
    
    for (const announcement of announcementsWithoutTitle) {
      try {
        const generatedTitle = generateTitleFromContent(announcement.content);
        await Announcement.findByIdAndUpdate(
          announcement._id,
          { 
            title: generatedTitle,
            updatedAt: new Date()
          }
        );
        updatedCount++;
        console.log(`✅ Cập nhật thông báo ${announcement._id}: "${generatedTitle}"`);
      } catch (error) {
        errorCount++;
        console.error(`❌ Lỗi cập nhật thông báo ${announcement._id}:`, error.message);
      }
    }
    
    console.log('\n📈 Kết quả migration:');
    console.log(`✅ Đã cập nhật: ${updatedCount} thông báo`);
    console.log(`❌ Lỗi: ${errorCount} thông báo`);
    console.log(`📊 Tổng cộng: ${announcementsWithoutTitle.length} thông báo`);
    
    const remainingWithoutTitle = await Announcement.countDocuments({
      $or: [
        { title: { $exists: false } },
        { title: '' },
        { title: null }
      ]
    });
    
    if (remainingWithoutTitle === 0) {
      console.log('🎉 Migration hoàn thành thành công! Tất cả thông báo đã có title.');
    } else {
      console.log(`⚠️  Còn ${remainingWithoutTitle} thông báo chưa có title. Vui lòng kiểm tra lại.`);
    }
    
  } catch (error) {
    console.error('💥 Lỗi trong quá trình migration:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Đã đóng kết nối database');
  }
}

if (require.main === module) {
  migrateAnnouncementTitles()
    .then(() => {
      console.log('✅ Migration script hoàn thành');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script thất bại:', error.message);
      process.exit(1);
    });
}

module.exports = { migrateAnnouncementTitles, generateTitleFromContent };