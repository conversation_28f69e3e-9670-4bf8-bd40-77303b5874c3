// routes/studentEnrollment.js - Routes cho quản lý ghi danh học sinh
const express = require('express');
const router = express.Router();
const {
  enrollStudent,
  transferStudent,
  getClassStudents,
  getStudentCurrentClass,
  getStudentHistory,
  updateEnrollmentStatus,
  getClassEnrollmentStats,
  getStudentCount
} = require('../controllers/studentEnrollmentController');
const { protect, authorize } = require('../middlewares/auth');

// @route   POST /api/enrollment
// @desc    Ghi danh học sinh vào lớp
// @access  Private (admin, teacher)
router.post(
  '/',
  protect,
  authorize('admin', 'teacher'),
  enrollStudent
);

// @route   PUT /api/enrollment/transfer
// @desc    Chuyển học sinh sang lớp khác
// @access  Private (admin, teacher)
router.put(
  '/transfer',
  protect,
  authorize('admin', 'teacher'),
  transferStudent
);

// @route   GET /api/enrollment/class/:classId
// @desc    Lấy danh sách học sinh của lớp
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
  '/class/:classId',
  protect,
  getClassStudents
);

// @route   GET /api/enrollment/student/:studentId/current
// @desc    Lấy lớp hiện tại của học sinh
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
  '/student/:studentId/current',
  protect,
  getStudentCurrentClass
);

// @route   GET /api/enrollment/student/:studentId/history
// @desc    Lấy lịch sử học tập của học sinh
// @access  Private (admin, teacher, chính học sinh đó)
router.get(
  '/student/:studentId/history',
  protect,
  getStudentHistory
);

// @route   PUT /api/enrollment/status
// @desc    Cập nhật trạng thái ghi danh
// @access  Private (admin)
router.put(
  '/status',
  protect,
  authorize('admin'),
  updateEnrollmentStatus
);

// @route   GET /api/enrollment/stats/class/:classId
// @desc    Lấy thống kê ghi danh theo lớp
// @access  Private (admin, teacher)
router.get(
  '/stats/class/:classId',
  protect,
  authorize('admin', 'teacher'),
  getClassEnrollmentStats
);

// @route   GET /api/enrollment/count/class/:classId
// @desc    Lấy số lượng học sinh trong lớp
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
  '/count/class/:classId',
  protect,
  getStudentCount
);

module.exports = router;
