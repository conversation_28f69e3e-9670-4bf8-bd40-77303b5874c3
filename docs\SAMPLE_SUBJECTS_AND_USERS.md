# Sample Subjects và User Bodies

## 1. Tạo Subjects trước (để có subjectIds)

### Subject Toán cơ bản:
```json
POST /api/subjects
{
  "name": "Toán 10",
  "code": "MATH10", 
  "department": "Toán",
  "description": "Toán học lớp 10",
  "category": "Tự nhiên"
}
```

### Subject Toán nâng cao:
```json
POST /api/subjects  
{
  "name": "Toán 11 Nâng cao",
  "code": "MATH11_ADV",
  "department": "Toán", 
  "description": "Toán học lớp 11 nâng cao",
  "category": "Tự nhiên"
}
```

### Subject Vật Lý:
```json
POST /api/subjects
{
  "name": "Vật Lý 10",
  "code": "PHYS10",
  "department": "Vật Lý",
  "description": "Vật lý lớp 10", 
  "category": "Tự nhiên"
}
```

---

## 2. <PERSON><PERSON> t<PERSON><PERSON> User (sử dụng subjectId thực tế)

### User 1: <PERSON><PERSON><PERSON><PERSON> viê<PERSON> (chỉ dạy 1 môn)
```json
POST /api/directory/user
{
  "name": "Nguyễn Văn An",
  "password": "123456789",
  "phoneNumber": "0901234561",
  "role": "teacher",
  "gender": "male",
  "specificRole": "subject_teacher",
  "teachingSubjects": ["680c9e3d04148f896098a3ab"],
  "department": "Toán"
}
```

### User 2: Giáo viên Toán (dạy nhiều môn trong cùng bộ môn)
```json
POST /api/directory/user
{
  "name": "Trần Thị Bình", 
  "password": "123456789",
  "phoneNumber": "0901234562",
  "role": "teacher",
  "gender": "female", 
  "specificRole": "subject_teacher",
  "teachingSubjects": ["680c9e3d04148f896098a3ab", "680c9e3d04148f896098a3ac"],
  "department": "Toán"
}
```

### User 3: Trưởng bộ môn Toán (có thể dạy nhiều bộ môn)
```json
POST /api/directory/user
{
  "name": "Phạm Văn Cường",
  "password": "123456789", 
  "phoneNumber": "0901234563",
  "role": "teacher",
  "gender": "male",
  "specificRole": "department_head",
  "teachingSubjects": ["680c9e3d04148f896098a3ab", "680c9e3d04148f896098a3ad"],
  "department": "Toán",
  "departmentHeadOf": "Toán"
}
```

---

## 3. Cách hoạt động sau khi tạo:

### User 1 sẽ có:
- `department`: "Toán" 
- `additionalDepartments`: []
- Chỉ thuộc bộ môn Toán

### User 2 sẽ có:
- `department`: "Toán"
- `additionalDepartments`: [] (vì cả 2 môn đều thuộc bộ môn Toán)
- Thuộc bộ môn Toán

### User 3 sẽ có:
- `department`: "Toán" (môn đầu tiên)
- `additionalDepartments`: ["Vật Lý"] (nếu môn thứ 2 thuộc bộ môn Vật Lý)
- `departmentHeadOf`: "Toán" 
- Có thể làm trưởng bộ môn Toán nhưng cũng dạy Vật Lý

---

## 4. Logic Announcement:

### Khi tạo thông báo cho bộ môn Toán:
```json
{
  "type": "principal_to_teacher",
  "content": "Họp bộ môn Toán",
  "recipients": {
    "department": "Toán"
  }
}
```

**Ai thấy được:**
- User 1: Có (department = "Toán")
- User 2: Có (department = "Toán") 
- User 3: Có (department = "Toán")

### Khi tạo thông báo cho bộ môn Vật Lý:
```json
{
  "type": "principal_to_teacher", 
  "content": "Họp bộ môn Vật Lý",
  "recipients": {
    "department": "Vật Lý"
  }
}
```

**Ai thấy được:**
- User 1: Không (chỉ dạy Toán)
- User 2: Không (chỉ dạy Toán)
- User 3: Có (nếu dạy cả Vật Lý - cần logic mở rộng)

---

## 5. Cần tối ưu thêm:

### Logic kiểm tra bộ môn trong announcement:
```javascript
// Trong buildAnnouncementQuery, thay đổi từ:
{ 'recipients.department': user.department }

// Thành:
{ 
  'recipients.department': { 
    $in: [user.department, ...user.additionalDepartments] 
  } 
}
```

### Method mới cho User:
```javascript
UserSchema.methods.teachesInDepartment = function(department) {
  return this.role === ROLES.TEACHER && 
         (this.department === department || 
          this.additionalDepartments.includes(department));
};
``` 