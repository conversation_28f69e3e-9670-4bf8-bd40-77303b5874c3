// models/StudentEnrollment.js - Model quản lý việc ghi danh học sinh vào lớp theo năm học
const mongoose = require('mongoose');

const StudentEnrollmentSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true
  },
  schoolYear: {
    type: String,
    required: true, // Ví dụ: "2024-2025"
    trim: true
  },
  enrollmentDate: {
    type: Date,
    default: Date.now,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'transferred', 'graduated', 'dropped'],
    default: 'active',
    required: true
  },
  // Ngày chuyển lớp hoặc kết thúc (nếu có)
  endDate: {
    type: Date
  },
  // Ghi chú về việc chuyển lớp
  notes: {
    type: String,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index để tối ưu hiệu suất truy vấn
StudentEnrollmentSchema.index({ student: 1, schoolYear: 1 });
StudentEnrollmentSchema.index({ class: 1, schoolYear: 1, status: 1 });
StudentEnrollmentSchema.index({ student: 1, class: 1, schoolYear: 1 }, { unique: true });

// Middleware để cập nhật updatedAt
StudentEnrollmentSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method để lấy học sinh hiện tại của lớp
StudentEnrollmentSchema.statics.getCurrentStudents = function(classId, schoolYear) {
  return this.find({
    class: classId,
    schoolYear: schoolYear,
    status: 'active'
  }).populate('student', 'name studentId phoneNumber');
};

// Static method để lấy lớp hiện tại của học sinh
StudentEnrollmentSchema.statics.getCurrentClass = function(studentId, schoolYear) {
  return this.findOne({
    student: studentId,
    schoolYear: schoolYear,
    status: 'active'
  }).populate('class', 'name group schoolYear');
};

// Static method để đếm số học sinh trong lớp
StudentEnrollmentSchema.statics.getStudentCount = function(classId, schoolYear) {
  return this.countDocuments({
    class: classId,
    schoolYear: schoolYear,
    status: 'active'
  });
};

module.exports = mongoose.model('StudentEnrollment', StudentEnrollmentSchema);
