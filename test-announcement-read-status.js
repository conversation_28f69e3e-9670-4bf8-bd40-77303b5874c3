/**
 * Test script để kiểm tra tính năng đánh dấu đã đọc thông báo
 * Chạy script này để test các API mới
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';
const API_URL = `${BASE_URL}/api`;

// Token test (thay bằng token thực tế)
let authToken = '';

// Helper function để gọi API
async function apiCall(method, endpoint, data = null, token = authToken) {
  try {
    const config = {
      method,
      url: `${API_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message 
    };
  }
}

async function testAnnouncementReadStatus() {
  console.log('🧪 Bắt đầu test tính năng đánh dấu đã đọc thông báo...\n');
  
  // 1. Test tạo thông báo mới với title (không dùng Zalo - auto sent)
  console.log('1️⃣ Test tạo thông báo mới với title (auto sent khi không dùng Zalo)...');
  const newAnnouncement = {
    title: 'Thông báo test đánh dấu đã đọc',
    type: 'teacher_to_student',
    content: 'Đây là nội dung thông báo test để kiểm tra tính năng đánh dấu đã đọc.',
    recipients: {
      class: '507f1f77bcf86cd799439011', // Thay bằng class ID thực tế
      subject: 'Toán học'
    },
    zaloConfig: {
      enabled: false // Tự động set status = 'sent'
    }
    // Không cần set status, sẽ tự động là 'sent'
  };
  
  const createResult = await apiCall('POST', '/announcements', newAnnouncement);
  if (createResult.success) {
    console.log('✅ Tạo thông báo thành công:', createResult.data._id);
    console.log('📊 Status tự động:', createResult.data.status); // Nên là 'sent'
    var testAnnouncementId = createResult.data._id;
  } else {
    console.log('❌ Lỗi tạo thông báo:', createResult.error);
    return;
  }

  // 1.1. Test tạo thông báo với Zalo enabled (status = draft)
  console.log('\n1️⃣.1️⃣ Test tạo thông báo với Zalo enabled (status = draft)...');
  const zaloAnnouncement = {
    title: 'Thông báo test với Zalo',
    type: 'teacher_to_student',
    content: 'Thông báo này sẽ được gửi qua Zalo.',
    recipients: {
      class: '507f1f77bcf86cd799439011',
      subject: 'Toán học'
    },
    zaloConfig: {
      enabled: true,
      groupId: 'test-group-id'
    }
    // Status sẽ mặc định là 'draft'
  };

  const createZaloResult = await apiCall('POST', '/announcements', zaloAnnouncement);
  if (createZaloResult.success) {
    console.log('✅ Tạo thông báo Zalo thành công:', createZaloResult.data._id);
    console.log('📊 Status mặc định:', createZaloResult.data.status); // Nên là 'draft'
  } else {
    console.log('❌ Lỗi tạo thông báo Zalo:', createZaloResult.error);
  }
  
  // 2. Test lấy danh sách thông báo (có trạng thái đọc)
  console.log('\n2️⃣ Test lấy danh sách thông báo với trạng thái đọc...');
  const listResult = await apiCall('GET', '/announcements?limit=5');
  if (listResult.success) {
    console.log('✅ Lấy danh sách thành công');
    console.log('📊 Số thông báo:', listResult.data.announcements.length);
    if (listResult.data.announcements.length > 0) {
      const firstAnnouncement = listResult.data.announcements[0];
      console.log('📋 Thông báo đầu tiên:');
      console.log(`   - Title: ${firstAnnouncement.title}`);
      console.log(`   - Đã đọc: ${firstAnnouncement.isRead}`);
    }
  } else {
    console.log('❌ Lỗi lấy danh sách:', listResult.error);
  }
  
  // 3. Test lấy danh sách thông báo chưa đọc
  console.log('\n3️⃣ Test lấy danh sách thông báo chưa đọc...');
  const unreadResult = await apiCall('GET', '/announcements/unread');
  if (unreadResult.success) {
    console.log('✅ Lấy danh sách chưa đọc thành công');
    console.log('📊 Số thông báo chưa đọc:', unreadResult.data.unreadCount);
  } else {
    console.log('❌ Lỗi lấy danh sách chưa đọc:', unreadResult.error);
  }
  
  // 4. Test đánh dấu đã đọc
  if (testAnnouncementId) {
    console.log('\n4️⃣ Test đánh dấu thông báo đã đọc...');
    const markReadResult = await apiCall('POST', `/announcements/${testAnnouncementId}/read`);
    if (markReadResult.success) {
      console.log('✅ Đánh dấu đã đọc thành công');
    } else {
      console.log('❌ Lỗi đánh dấu đã đọc:', markReadResult.error);
    }
  }
  
  // 5. Test lấy chi tiết thông báo (tự động đánh dấu đã đọc)
  if (testAnnouncementId) {
    console.log('\n5️⃣ Test lấy chi tiết thông báo...');
    const detailResult = await apiCall('GET', `/announcements/${testAnnouncementId}`);
    if (detailResult.success) {
      console.log('✅ Lấy chi tiết thành công');
      console.log(`📋 Title: ${detailResult.data.title}`);
      console.log(`📖 Đã đọc: ${detailResult.data.isRead}`);
    } else {
      console.log('❌ Lỗi lấy chi tiết:', detailResult.error);
    }
  }
  
  // 6. Test lấy thống kê đọc (chỉ cho admin/người tạo)
  if (testAnnouncementId) {
    console.log('\n6️⃣ Test lấy thống kê đọc thông báo...');
    const statsResult = await apiCall('GET', `/announcements/${testAnnouncementId}/read-status`);
    if (statsResult.success) {
      console.log('✅ Lấy thống kê thành công');
      console.log('📊 Thống kê:', statsResult.data.stats);
    } else {
      console.log('❌ Lỗi lấy thống kê (có thể do không có quyền):', statsResult.error);
    }
  }
  
  // 7. Test cập nhật thông báo với title
  if (testAnnouncementId) {
    console.log('\n7️⃣ Test cập nhật thông báo với title mới...');
    const updateData = {
      title: 'Thông báo test đã được cập nhật',
      content: 'Nội dung đã được cập nhật để test tính năng mới.'
    };
    const updateResult = await apiCall('PUT', `/announcements/${testAnnouncementId}`, updateData);
    if (updateResult.success) {
      console.log('✅ Cập nhật thông báo thành công');
      console.log(`📋 Title mới: ${updateResult.data.title}`);
    } else {
      console.log('❌ Lỗi cập nhật thông báo:', updateResult.error);
    }
  }
  
  console.log('\n🎉 Hoàn thành test tính năng đánh dấu đã đọc thông báo!');
}

// Hàm để set auth token
function setAuthToken(token) {
  authToken = token;
  console.log('🔑 Đã set auth token');
}

// Export functions để có thể sử dụng từ bên ngoài
module.exports = {
  testAnnouncementReadStatus,
  setAuthToken,
  apiCall
};

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  console.log('⚠️  Lưu ý: Cần set auth token trước khi chạy test');
  console.log('💡 Sử dụng: setAuthToken("your-token-here") trước khi chạy test');
  
  // Uncomment dòng dưới và thay bằng token thực tế để test
  // setAuthToken('your-actual-token-here');
  // testAnnouncementReadStatus();
}
