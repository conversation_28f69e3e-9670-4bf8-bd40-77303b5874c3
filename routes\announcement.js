// src/routes/announcementRoutes.js
const express = require('express');
const router = express.Router();
const {
  createAnnouncement,
  getAnnouncements,
  getRecentAnnouncements,
  getAnnouncementById,
  updateAnnouncement,
  deleteAnnouncement,
  getAnnouncementConfig,
  getAnnouncementStats,
  getUserZaloGroupsForAnnouncement,
  sendZaloMessage,
  markAnnouncementAsRead,
  getAnnouncementReadStatus,
  getUnreadAnnouncements
} = require('../controllers/announcementController');
const { protect, authorize } = require('../middlewares/auth');

// @route   POST /api/announcements
// @desc    Tạo thông báo mới
// @access  Private (yêu cầu x-auth-token)
router.post('/', protect, createAnnouncement);

// @route   GET /api/announcements
// @desc    Lấy danh sách thông báo (phân trang, có filter)
// @access  Private (yêu cầu x-auth-token)
router.get('/', protect, getAnnouncements);

// @route   GET /api/announcements/recent
// @desc    Lấy thông báo mới nhất
// @access  Private (yêu cầu x-auth-token)
router.get('/recent', protect, getRecentAnnouncements);

// @route   GET /api/announcements/unread
// @desc    Lấy danh sách thông báo chưa đọc
// @access  Private (yêu cầu x-auth-token)
router.get('/unread', protect, getUnreadAnnouncements);

// @route   GET /api/announcements/config
// @desc    Lấy cấu hình thông báo (types, statuses, departments)
// @access  Private (yêu cầu x-auth-token)
router.get('/config', protect, getAnnouncementConfig);

// @route   GET /api/announcements/stats
// @desc    Lấy thống kê thông báo
// @access  Private (yêu cầu x-auth-token)
router.get('/stats', protect, getAnnouncementStats);

// @route   GET /api/announcements/zalo-groups
// @desc    Lấy nhóm Zalo của user cho announcement
// @access  Private (yêu cầu x-auth-token)
router.get('/zalo-groups', protect, getUserZaloGroupsForAnnouncement);

// @route   GET /api/announcements/:id
// @desc    Lấy chi tiết thông báo
// @access  Private (yêu cầu x-auth-token)
router.get('/:id', protect, getAnnouncementById);

// @route   POST /api/announcements/:id/read
// @desc    Đánh dấu thông báo đã đọc
// @access  Private (yêu cầu x-auth-token)
router.post('/:id/read', protect, markAnnouncementAsRead);

// @route   GET /api/announcements/:id/read-status
// @desc    Lấy thống kê đọc thông báo
// @access  Private (yêu cầu x-auth-token)
router.get('/:id/read-status', protect, getAnnouncementReadStatus);

// @route   PUT /api/announcements/:id
// @desc    Cập nhật thông báo
// @access  Private (yêu cầu x-auth-token)
router.put('/:id', protect, updateAnnouncement);

// @route   DELETE /api/announcements/:id
// @desc    Xóa thông báo
// @access  Private (yêu cầu x-auth-token)
router.delete('/:id', protect, deleteAnnouncement);

// @route   POST /api/announcements/:id/send-zalo
// @desc    Gửi thông báo qua Zalo ngay lập tức
// @access  Private (yêu cầu x-auth-token)
router.post('/:id/send-zalo', protect, sendZaloMessage);

module.exports = router;