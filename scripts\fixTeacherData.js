// scripts/fixTeacherData.js - Script để kiểm tra và sửa dữ liệu giáo viên
const mongoose = require('mongoose');
require('dotenv').config();

async function fixTeacherData() {
  try {
    console.log('🚀 Bắt đầu kiểm tra và sửa dữ liệu giáo viên...');
    
    // Kết nối database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Đã kết nối database');

    const db = mongoose.connection.db;
    const teacherId = '680c9e4d04148f896098a3b2';
    
    // 1. Kiểm tra thông tin giáo viên
    console.log('\n📋 KIỂM TRA THÔNG TIN GIÁO VIÊN:');
    const teacher = await db.collection('users').findOne({ 
      _id: new mongoose.Types.ObjectId(teacherId) 
    });
    
    if (!teacher) {
      console.log('❌ Không tìm thấy giáo viên với ID:', teacherId);
      return;
    }
    
    console.log('👨‍🏫 Thông tin giáo viên:');
    console.log(`   - Tên: ${teacher.name}`);
    console.log(`   - Role: ${teacher.role}`);
    console.log(`   - Specific Role: ${teacher.specificRole}`);
    console.log(`   - Department: ${teacher.department}`);
    console.log(`   - Teaching Subjects: ${teacher.teachingSubjects?.length || 0} môn`);
    
    // 2. Kiểm tra các lớp mà giáo viên làm chủ nhiệm
    console.log('\n📚 KIỂM TRA LỚP CHỦ NHIỆM:');
    const homeroomClasses = await db.collection('classes').find({
      homeroomTeacher: new mongoose.Types.ObjectId(teacherId)
    }).toArray();
    
    console.log(`🏫 Giáo viên làm chủ nhiệm ${homeroomClasses.length} lớp:`);
    homeroomClasses.forEach(cls => {
      console.log(`   - ${cls.name} (${cls.schoolYear})`);
    });
    
    // 3. Kiểm tra các lớp mà giáo viên dạy môn
    console.log('\n📖 KIỂM TRA LỚP DẠY MÔN:');
    const subjectClasses = await db.collection('classes').find({
      'subjectTeachers.teacher': new mongoose.Types.ObjectId(teacherId)
    }).toArray();
    
    console.log(`📝 Giáo viên dạy môn tại ${subjectClasses.length} lớp:`);
    subjectClasses.forEach(cls => {
      const subjects = cls.subjectTeachers
        .filter(st => st.teacher.toString() === teacherId)
        .map(st => st.subject);
      console.log(`   - ${cls.name} (${cls.schoolYear}): ${subjects.join(', ')}`);
    });
    
    // 4. Kiểm tra dữ liệu điểm danh mà giáo viên tạo
    console.log('\n✅ KIỂM TRA DỮ LIỆU ĐIỂM DANH:');
    const attendanceCreated = await db.collection('attendances').countDocuments({
      createdBy: new mongoose.Types.ObjectId(teacherId)
    });
    
    const attendanceUpdated = await db.collection('attendances').countDocuments({
      updatedBy: new mongoose.Types.ObjectId(teacherId)
    });
    
    const attendanceRejected = await db.collection('attendances').countDocuments({
      rejectedBy: new mongoose.Types.ObjectId(teacherId)
    });
    
    console.log(`📊 Thống kê điểm danh:`);
    console.log(`   - Tạo: ${attendanceCreated} bản ghi`);
    console.log(`   - Cập nhật: ${attendanceUpdated} bản ghi`);
    console.log(`   - Từ chối: ${attendanceRejected} bản ghi`);
    
    // 5. Kiểm tra StudentEnrollment cho các lớp của giáo viên
    console.log('\n🎓 KIỂM TRA STUDENT ENROLLMENT:');
    
    for (const cls of homeroomClasses) {
      const enrollmentCount = await db.collection('studentenrollments').countDocuments({
        class: cls._id,
        schoolYear: cls.schoolYear,
        status: 'active'
      });
      
      console.log(`📋 Lớp ${cls.name}: ${enrollmentCount} học sinh đã enrollment`);
      
      // Kiểm tra xem có học sinh nào trong attendance nhưng không có enrollment không
      const attendanceStudents = await db.collection('attendances').distinct('student', {
        class: cls._id
      });
      
      const enrolledStudents = await db.collection('studentenrollments').distinct('student', {
        class: cls._id,
        schoolYear: cls.schoolYear,
        status: 'active'
      });
      
      const missingEnrollments = attendanceStudents.filter(studentId => 
        !enrolledStudents.some(enrolledId => enrolledId.toString() === studentId.toString())
      );
      
      if (missingEnrollments.length > 0) {
        console.log(`⚠️  Lớp ${cls.name}: ${missingEnrollments.length} học sinh có điểm danh nhưng thiếu enrollment`);
        
        // Tạo enrollment cho những học sinh thiếu
        for (const studentId of missingEnrollments) {
          const student = await db.collection('users').findOne({ _id: studentId });
          if (student && student.role === 'student') {
            const enrollmentData = {
              student: studentId,
              class: cls._id,
              schoolYear: cls.schoolYear,
              group: student.group || 'A',
              status: 'active',
              enrollmentDate: student.createdAt || new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };
            
            await db.collection('studentenrollments').insertOne(enrollmentData);
            console.log(`   ✅ Đã tạo enrollment cho học sinh: ${student.name}`);
          }
        }
      }
    }
    
    // 6. Kiểm tra và sửa dữ liệu attendance statistics
    console.log('\n📈 KIỂM TRA ATTENDANCE STATISTICS:');
    
    for (const cls of homeroomClasses) {
      try {
        // Test API attendance statistics
        const StudentEnrollmentService = require('../services/studentEnrollmentService');
        const studentCount = await StudentEnrollmentService.getStudentCount(
          cls._id.toString(), 
          cls.schoolYear
        );
        
        console.log(`📊 Lớp ${cls.name}: API trả về ${studentCount} học sinh`);
        
        // Kiểm tra attendance records
        const attendanceCount = await db.collection('attendances').countDocuments({
          class: cls._id
        });
        
        console.log(`   - Có ${attendanceCount} bản ghi điểm danh`);
        
      } catch (error) {
        console.error(`❌ Lỗi khi test API cho lớp ${cls.name}:`, error.message);
      }
    }
    
    // 7. Tóm tắt kết quả
    console.log('\n🎯 TÓM TẮT:');
    console.log(`👨‍🏫 Giáo viên: ${teacher.name}`);
    console.log(`🏫 Chủ nhiệm: ${homeroomClasses.length} lớp`);
    console.log(`📚 Dạy môn: ${subjectClasses.length} lớp`);
    console.log(`✅ Điểm danh: ${attendanceCreated + attendanceUpdated + attendanceRejected} hoạt động`);
    
    console.log('\n✅ Kiểm tra và sửa dữ liệu hoàn thành!');
    
  } catch (error) {
    console.error('💥 Lỗi nghiêm trọng:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Đã ngắt kết nối database');
  }
}

// Chạy script
if (require.main === module) {
  fixTeacherData()
    .then(() => {
      console.log('✅ Script hoàn thành');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script thất bại:', error);
      process.exit(1);
    });
}

module.exports = fixTeacherData;
