// src/controllers/authController.js
const User = require('../models/User');
const jwt = require('jsonwebtoken');
const config = require('../config/default');
const mongoose = require('mongoose');
const Class = require('../models/Class');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roles');
const { GENDER } = require('../constants/userConstants');
const { HTTP_STATUS, createSuccessResponse, createErrorResponse } = require('../constants/httpConstants');
const zaloService = require('../services/zaloService');

// @desc    Lưu code_verifier và state từ frontend
// @route   POST /api/auth/set-verifier
// @access  Public
exports.setVerifier = (req, res) => {
  const { codeVerifier, state } = req.body;

  if (!codeVerifier || !state) {
    console.error('Thiếu codeVerifier hoặc state:', req.body);
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  req.session.zalo_code_verifier = codeVerifier;
  req.session.zalo_state = state;

  console.log('Đã lưu code_verifier và state vào session:', { codeVerifier, state });
  res.status(HTTP_STATUS.OK).json(createSuccessResponse(null, 'Verifier and state stored'));
};

// @desc    Xác thực với Zalo
// @route   GET /api/auth/zalo/callback
// @access  Public
exports.zaloCallback = async (req, res) => {
  const { code, state } = req.query;

  if (!code) {
    console.error('Không có mã xác thực trong query:', req.query);
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  const storedState = req.session.zalo_state;
  if (!storedState || state !== storedState) {
    console.error('State không hợp lệ:', { receivedState: state, storedState });
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  try {
    const codeVerifier = req.session.zalo_code_verifier;
    if (!codeVerifier) {
      console.error('Thiếu code_verifier trong session');
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
      );
    }

    // Đổi code lấy access token
    const { access_token, refresh_token, expires_in } = await zaloService.exchangeCodeForToken(code, codeVerifier);
    console.log('Access Token:', access_token);
    console.log('Refresh Token:', refresh_token);
    console.log('Expires In:', expires_in);

    // Lấy thông tin người dùng từ Zalo
    console.log('Đang lấy thông tin người dùng từ Zalo...');
    const userInfo = await zaloService.getUserInfo(access_token);
    const { id: zaloId, name, picture } = userInfo;
    console.log('Zalo User Info:', { zaloId, name, picture });

    let user = await User.findOne({ zaloId });

    if (!user) {
      user = new User({
        name,
        zaloId,
        avatar: picture?.data?.url,
        role: ROLES.STUDENT,
        studentId: `STU-${zaloId}`, // Tạo studentId tạm, bạn có thể yêu cầu người dùng nhập sau
        password: 'default_password', // Tạo mật khẩu mặc định, bạn có thể yêu cầu đổi sau
        phoneNumber: 'unknown', // Giá trị tạm, sẽ cập nhật sau khi lấy số điện thoại
        gender: GENDER.OTHER, // Giá trị mặc định cho gender
      });
      await user.save();
      console.log('Người dùng mới đã được tạo:', user);
    } else {
      console.log('Người dùng đã tồn tại:', user);
    }

    const payload = {
      user: {
        id: user.id,
        role: user.role,
      },
    };

    jwt.sign(
      payload,
      config.jwtSecret,
      { expiresIn: '24h' },
      (err, token) => {
        if (err) {
          console.error('Lỗi khi tạo JWT token:', err);
          throw err;
        }
        const redirectUrl = `${process.env.FRONTEND_URL}/dashboard?token=${token}`;
        console.log('Redirecting to:', redirectUrl);
        res.redirect(redirectUrl);
      }
    );
  } catch (err) {
    console.error('Lỗi xác thực Zalo:', {
      error: err.response?.data || err.message,
      code,
      state,
      codeVerifier: req.session.zalo_code_verifier,
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse(MESSAGES.ERROR.GENERAL, err.response?.data)
    );
  }
};

// @desc    Lấy thông tin người dùng hiện tại
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    // Lấy thông tin user không populate class vì đã chuyển sang StudentEnrollment
    const user = await User.findById(req.user.id)
      .select('-password');

    if (!user) {
      console.error('Không tìm thấy người dùng:', req.user.id);
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        createErrorResponse(MESSAGES.USER.NOT_FOUND)
      );
    }

    // Lấy thông tin displayRole và displayMeta từ phương thức getContactInfo
    const contactInfo = user.getContactInfo();

    // Chuyển đổi user thành object để có thể chỉnh sửa
    const userObj = user.toObject();

    // Nếu là học sinh, lấy thông tin lớp hiện tại từ StudentEnrollment
    if (user.role === 'student') {
      try {
        const StudentEnrollmentService = require('../services/studentEnrollmentService');
        const { getCurrentSchoolYear } = require('../utils/schoolYear');

        const schoolYear = getCurrentSchoolYear();

        const currentClass = await StudentEnrollmentService.getStudentCurrentClass(
          user._id,
          schoolYear
        );

        if (currentClass && currentClass.class) {
          userObj.class = {
            id: currentClass.class._id,
            name: currentClass.class.name,
            group: currentClass.group,
            schoolYear: currentClass.schoolYear
          };
        }
      } catch (classError) {
        console.warn('Không thể lấy thông tin lớp cho học sinh:', classError.message);
        // Không throw error, chỉ log warning
      }
    }

    // Tạo đối tượng kết quả bao gồm thông tin người dùng và displayRole, displayMeta
    const userResponse = {
      ...userObj,
      displayRole: contactInfo.displayRole,
      displayMeta: contactInfo.displayMeta
    };

    console.log('Lấy thông tin người dùng thành công:', userResponse);
    res.json(userResponse);
  } catch (err) {
    console.error('Lỗi khi lấy thông tin người dùng:', err.message);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse(MESSAGES.ERROR.GENERAL)
    );
  }
};

// @desc    Đăng nhập bằng mã số học sinh và mật khẩu
// @route   POST /api/register
// @access  Public
exports.postRegister = async (req, res) => {
  const { 
    name, 
    studentId, 
    password, 
    phoneNumber, 
    role, 
    class: classId,
    gender,
    specificRole,
    teachingSubjects,
    group
  } = req.body;

  try {
    // Kiểm tra người dùng tồn tại (theo studentId hoặc phoneNumber)
    let existingUser = await User.findOne({
      $or: [
        { studentId: studentId },
        { phoneNumber: phoneNumber }
      ]
    });
    
    if (existingUser) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(
          studentId && existingUser.studentId === studentId 
            ? MESSAGES.USER.ALREADY_EXISTS 
            : 'Số điện thoại đã được sử dụng'
        )
      );
    }

    // Validate theo role
    if (role === ROLES.STUDENT) {
      // Validation cho học sinh
      if (!classId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createErrorResponse(MESSAGES.USER.MISSING_CLASS)
        );
      }
      if (!mongoose.Types.ObjectId.isValid(classId)) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createErrorResponse(MESSAGES.USER.INVALID_CLASS_ID)
        );
      }
      
      const classExists = await Class.findById(classId);
      if (!classExists) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          createErrorResponse(MESSAGES.CLASS.NOT_FOUND)
        );
      }
      
      if (!studentId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createErrorResponse(MESSAGES.USER.MISSING_STUDENT_ID)
        );
      }
    } else if (role === ROLES.TEACHER) {
      // Validation cho giáo viên
      if (!teachingSubjects || !Array.isArray(teachingSubjects) || teachingSubjects.length === 0) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createErrorResponse(MESSAGES.USER.MISSING_TEACHING_SUBJECTS)
        );
      }
      
      // Kiểm tra các môn học có tồn tại không
      const Subject = require('../models/Subject');
      const validSubjects = await Subject.find({ _id: { $in: teachingSubjects } });
      if (validSubjects.length !== teachingSubjects.length) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          createErrorResponse(MESSAGES.USER.INVALID_TEACHING_SUBJECTS)
        );
      }
    }

    // Tạo user mới
    const userData = {
      name,
      password,
      phoneNumber,
      role,
      gender: gender || GENDER.OTHER,
      specificRole
    };

    // Thêm fields theo role
    if (role === ROLES.STUDENT) {
      userData.studentId = studentId;
      // class và group đã được chuyển sang StudentEnrollment model
    } else if (role === ROLES.TEACHER) {
      userData.teachingSubjects = teachingSubjects;
      // department sẽ được tự động set trong pre('save') hook dựa trên teachingSubjects
    }

    const user = new User(userData);
    await user.save();

    // Populate teachingSubjects để trả về thông tin đầy đủ
    if (role === ROLES.TEACHER) {
      await user.populate('teachingSubjects', 'name department');
    }

    // Tạo JWT
    const payload = { user: { id: user.id, role: user.role } };
    const token = jwt.sign(payload, config.jwtSecret, { expiresIn: '1h' });

    // Loại bỏ password khỏi response
    const userResponse = user.toObject();
    delete userResponse.password;

    res.json({ token, user: userResponse });
  } catch (err) {
    console.error('Error in postRegister:', err.message);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse(MESSAGES.ERROR.GENERAL)
    );
  }
};

// @desc    Đăng nhập hoặc tạo người dùng bằng số điện thoại từ Zalo
// @route   POST /api/auth/phone
// @access  Public
exports.getByPhoneNumber = async (req, res) => {
  const { token, accessToken } = req.body;

  if (!token || !accessToken) {
    console.error('Thiếu token hoặc accessToken trong body:', req.body);
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  try {
    // Gọi API Zalo để lấy số điện thoại
    const zaloResponse = await zaloService.getPhoneNumber(token, accessToken);

    // Kiểm tra response từ Zalo
    const { data, error, message } = zaloResponse;
    if (error !== 0) {
      console.error('Zalo API trả về lỗi:', { error, message });
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.USER.INVALID_CREDENTIALS, message)
      );
    }

    // Lấy số điện thoại từ data
    const { number: phoneNumber } = data || {};
    if (!phoneNumber) {
      console.error('Không tìm thấy số điện thoại trong response:', zaloResponse);
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.USER.INVALID_CREDENTIALS)
      );
    }

    // Chuẩn hóa và tạo các biến thể của số điện thoại để tìm kiếm
    const { phoneVariants, normalizedPhoneNumber } = zaloService.normalizePhoneNumber(phoneNumber);

    console.log('Các biến thể số điện thoại để tìm kiếm:', phoneVariants);

    // Tìm người dùng theo các biến thể số điện thoại
    let user = await User.findOne({ phoneNumber: { $in: phoneVariants } });

    if (!user) {
      // Tìm một lớp mặc định để gán cho người dùng mới
      const defaultClass = await Class.findOne({});

      // Xác định vai trò mặc định dựa trên việc có lớp hay không
      const defaultRole = defaultClass ? ROLES.STUDENT : ROLES.TEACHER;

      // Tạo người dùng mới nếu chưa tồn tại
      user = new User({
        name: 'User', // Mặc định, vì response không có name
        phoneNumber: normalizedPhoneNumber,
        studentId: defaultRole === ROLES.STUDENT ? `STU-${Date.now()}` : undefined, // Chỉ tạo studentId cho học sinh
        password: 'default_password', // Mật khẩu mặc định, yêu cầu đổi sau
        role: defaultRole, // Nếu không có lớp, đặt vai trò là giáo viên
        gender: GENDER.OTHER, // Giá trị mặc định cho gender
        group: defaultRole === ROLES.STUDENT ? 'A' : undefined, // Chỉ cần group cho học sinh
        class: defaultClass ? defaultClass._id : undefined, // Gán lớp mặc định nếu có
      });

      console.log('Đang tạo người dùng mới với vai trò:', defaultRole);
      await user.save();
      console.log('Người dùng mới đã được tạo:', user);
    } else {
      console.log('Người dùng đã tồn tại:', {
        name: user.name,
        role: user.role,
        phoneNumber: user.phoneNumber
      });
    }

    // Tạo JWT token
    const payload = {
      user: {
        id: user._id.toString(), // Đảm bảo lưu dưới dạng chuỗi
        role: user.role,
      },
    };

    console.log('Payload được tạo:', payload);

    jwt.sign(
      payload,
      config.jwtSecret,
      { expiresIn: '24h' },
      (err, token) => {
        if (err) {
          console.error('Lỗi khi tạo JWT token:', err);
          throw err;
        }
        console.log('Đăng nhập bằng số điện thoại thành công:', normalizedPhoneNumber);

        // Giải mã token luôn để kiểm tra
        try {
          const decoded = jwt.verify(token, config.jwtSecret);
          console.log('Token sau khi giải mã:', decoded);
        } catch (decodeErr) {
          console.error('Lỗi khi giải mã token mới tạo:', decodeErr);
        }

        res.json({
          token,
          user: {
            id: user._id,
            name: user.name,
            phoneNumber: user.phoneNumber, // Trả về số điện thoại đã lưu trong DB
            role: user.role
          }
        });
      }
    );
  } catch (err) {
    console.error('Lỗi khi xử lý số điện thoại:', {
      error: err.response?.data || err.message,
      token,
    });
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse(MESSAGES.ERROR.GENERAL)
    );
  }
};

// @desc    Đăng nhập bằng mã số học sinh/số điện thoại và mật khẩu
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res) => {
  const { studentId, password } = req.body;

  console.log('Request body:', req.body);

  if (!studentId || !password) {
    console.error('Thiếu studentId hoặc password trong body:', req.body);
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  try {
    // Tìm kiếm người dùng theo studentId hoặc phoneNumber
    const user = await User.findOne({
      $or: [
        { studentId },
        { phoneNumber: studentId } // Tìm theo phoneNumber nếu studentId không tìm thấy
      ]
    });

    if (!user) {
      console.error('Không tìm thấy người dùng với mã số/số điện thoại:', studentId);
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.USER.INVALID_CREDENTIALS)
      );
    }

    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      console.error('Mật khẩu không đúng cho người dùng:', user.name);
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.USER.INVALID_CREDENTIALS)
      );
    }

    // Các log để debug ID
    console.log('User info:', {
      name: user.name,
      role: user.role,
      phoneNumber: user.phoneNumber
    });
    console.log('User _id (raw):', user._id);
    console.log('User _id (toString):', user._id.toString());
    console.log('User _id (toHexString):', user._id.toHexString ? user._id.toHexString() : 'không có phương thức toHexString');

    // Nếu cần, kiểm tra trong DB trực tiếp để so sánh
    console.log('Để kiểm tra trong MongoDB, chạy lệnh sau:');
    console.log(`db.users.findOne({phoneNumber: "${user.phoneNumber}"}, {_id: 1})`);

    const payload = {
      user: {
        id: user._id.toString(), // Đảm bảo lưu dưới dạng chuỗi
        role: user.role,
      },
    };

    console.log('Payload được tạo:', payload);

    jwt.sign(
      payload,
      config.jwtSecret,
      { expiresIn: '24h' },
      (err, token) => {
        if (err) {
          console.error('Lỗi khi tạo JWT token:', err);
          throw err;
        }
        console.log('Đăng nhập thành công với người dùng:', user.name);

        // Giải mã token luôn để kiểm tra
        try {
          const decoded = jwt.verify(token, config.jwtSecret);
          console.log('Token sau khi giải mã:', decoded);
        } catch (decodeErr) {
          console.error('Lỗi khi giải mã token mới tạo:', decodeErr);
        }

        res.json({ token });
      }
    );
  } catch (err) {
    console.error('Lỗi khi đăng nhập:', err.message);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse(MESSAGES.ERROR.GENERAL)
    );
  }
};