# Ví dụ về các trường hợp thông báo

## C<PERSON>u trúc User Model (<PERSON>uan trọng!)

### Teacher phải có:
```javascript
{
  "role": "teacher",
  "name": "<PERSON><PERSON><PERSON><PERSON>",
  "department": "<PERSON><PERSON>", // REQUIRED - từ DEPARTMENTS constant
  "specificRole": "subject_teacher", // Hoặc "homeroom_teacher", "department_head"
  "teachingSubject": "objectId", // Ref tới Subject model
  
  // Nếu là department_head:
  "departmentHeadOf": "Toán" // PHẢI TRÙNG với department
}
```

### Department values hợp lệ:
- "To<PERSON>", "Văn", "<PERSON><PERSON>ế<PERSON> An<PERSON>", "<PERSON><PERSON><PERSON> Lý", "<PERSON><PERSON><PERSON> H<PERSON>", "<PERSON><PERSON> H<PERSON>"
- "L<PERSON><PERSON>ử", "Đị<PERSON> Lý", "GDCD", "Thể Dục", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>hu<PERSON>", "<PERSON><PERSON>", "<PERSON> H<PERSON>"

---

## 1. Teacher thông báo cho học sinh lớp

### Body request:
```json
{
  "type": "teacher_to_student",
  "content": "<PERSON><PERSON><PERSON> bài tập chương 1 vào thứ 2 tuần sau",
  "recipients": {
    "class": "60f7b1b8e4b0a8b5c8d9e0f1",
    "subject": "Toán học"
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "123456789"
  }
}
```

### Ai thấy được:
- **Học sinh trong lớp đó**: Thấy khi status = "sent"
- **Teacher tạo**: Thấy tất cả trạng thái (draft, scheduled, sent)
- **Admin**: Thấy tất cả

---

## 2. Hiệu trưởng thông báo cho TẤT CẢ giáo viên

### Body request:
```json
{
  "type": "principal_to_teacher",
  "content": "Họp toàn thể giáo viên vào 8h sáng thứ 2",
  "recipients": {
    "schoolWide": true
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "987654321"
  }
}
```

### Ai thấy được:
- **Tất cả giáo viên**: Thấy khi status = "sent"
- **Admin tạo**: Thấy tất cả trạng thái
- **Admin khác**: Thấy tất cả

---

## 3. Hiệu trưởng thông báo cho giáo viên ĐƯỢC CHỈ ĐỊNH

### Body request:
```json
{
  "type": "principal_to_teacher", 
  "content": "Mời tham gia hội đồng giáo viên",
  "recipients": {
    "teachers": ["60f7b1b8e4b0a8b5c8d9e0f2", "60f7b1b8e4b0a8b5c8d9e0f3"]
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "111222333"
  }
}
```

### Ai thấy được:
- **Giáo viên trong danh sách**: Thấy khi status = "sent"
- **Admin tạo**: Thấy tất cả trạng thái  
- **Admin khác**: Thấy tất cả

---

## 4. Hiệu trưởng thông báo cho giáo viên THEO BỘ MÔN

### Body request:
```json
{
  "type": "principal_to_teacher",
  "content": "Họp tổ bộ môn Toán vào chiều thứ 3", 
  "recipients": {
    "department": "Toán"
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "444555666"
  }
}
```

### Ai thấy được:
- **Giáo viên có user.department = "Toán"**: Thấy khi status = "sent"
- **Admin tạo**: Thấy tất cả trạng thái
- **Admin khác**: Thấy tất cả

---

## 5. Tổ trưởng thông báo cho giáo viên trong tổ

### Quyền tạo:
- User có `role: "teacher"` và `specificRole: "department_head"`
- Và `departmentHeadOf: "Lý"` (ví dụ tổ trưởng môn Lý)

### Body request:
```json
{
  "type": "head_to_teacher",
  "content": "Họp tổ môn Lý để bàn về chương trình mới",
  "recipients": {
    "department": "Lý"  
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "777888999"
  }
}
```

### Ai thấy được:
- **Giáo viên có user.department = "Lý"**: Thấy khi status = "sent"
- **Tổ trưởng tạo**: Thấy tất cả trạng thái
- **Admin**: Thấy tất cả

---

## 6. Admin thông báo toàn trường

### Body request:
```json
{
  "type": "admin_to_all",
  "content": "Nghỉ học do bão vào ngày mai",
  "recipients": {
    "schoolWide": true
  },
  "zaloConfig": {
    "enabled": true, 
    "groupId": "000111222"
  }
}
```

### Ai thấy được:
- **Tất cả user (học sinh, giáo viên)**: Thấy khi status = "sent"
- **Admin tạo**: Thấy tất cả trạng thái
- **Admin khác**: Thấy tất cả

---

## Ví dụ tạo User

### Tạo giáo viên môn Toán:
```json
{
  "name": "Nguyễn Văn A",
  "role": "teacher",
  "department": "Toán",
  "specificRole": "subject_teacher",
  "teachingSubject": "60f7b1b8e4b0a8b5c8d9e0f1"
}
```

### Tạo trưởng bộ môn Toán:
```json
{
  "name": "Trần Thị B", 
  "role": "teacher",
  "department": "Toán",
  "specificRole": "department_head",
  "departmentHeadOf": "Toán",
  "teachingSubject": "60f7b1b8e4b0a8b5c8d9e0f1"
}
```

## Logic tóm tắt:

### Student:
- Chỉ thấy thông báo `teacher_to_student` cho lớp mình và `admin_to_all`
- Chỉ thấy khi status = "sent"

### Teacher:  
- Thấy thông báo mình tạo (tất cả trạng thái)
- Thấy thông báo `principal_to_teacher`, `head_to_teacher` khi:
  - Gửi toàn trường (`schoolWide: true`)
  - Được chỉ định trong danh sách `teachers`
  - Thuộc bộ môn được chỉ định (`recipients.department` = `user.department`)
- Thấy `admin_to_all` gửi toàn trường
- Chỉ thấy thông báo từ người khác khi status = "sent"

### Admin:
- Thấy tất cả thông báo với tất cả trạng thái

### Department Head:
- Có thể tạo `head_to_teacher` cho bộ môn mình quản lý
- Logic xem thông báo giống Teacher thường 