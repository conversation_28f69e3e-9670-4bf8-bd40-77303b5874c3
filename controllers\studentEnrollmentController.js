// controllers/studentEnrollmentController.js - Controller cho quản lý ghi danh học sinh
const StudentEnrollmentService = require('../services/studentEnrollmentService');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const { ROLES } = require('../constants/roles');

// @desc    Ghi danh học sinh vào lớp
// @route   POST /api/enrollment
// @access  Private (admin, giáo viên chủ nhiệm)
exports.enrollStudent = asyncHandler(async (req, res) => {
  const { studentId, classId, schoolYear, group } = req.body;

  // Kiểm tra quyền truy cập
  if (req.user.role !== ROLES.ADMIN && req.user.role !== ROLES.TEACHER) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.enrollStudent(
      studentId, 
      classId, 
      schoolYear, 
      group
    );

    res.status(201).json({
      success: true,
      data: enrollment,
      msg: 'Ghi danh học sinh thành công'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Chuyển học sinh sang lớp khác
// @route   PUT /api/enrollment/transfer
// @access  Private (admin, giáo viên chủ nhiệm)
exports.transferStudent = asyncHandler(async (req, res) => {
  const { studentId, newClassId, schoolYear, reason } = req.body;

  // Kiểm tra quyền truy cập
  if (req.user.role !== ROLES.ADMIN && req.user.role !== ROLES.TEACHER) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.transferStudent(
      studentId, 
      newClassId, 
      schoolYear, 
      reason
    );

    res.json({
      success: true,
      data: enrollment,
      msg: 'Chuyển lớp thành công'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy danh sách học sinh của lớp
// @route   GET /api/enrollment/class/:classId
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getClassStudents = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear, status = 'active' } = req.query;

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const enrollments = await StudentEnrollmentService.getClassStudents(
      classId, 
      schoolYear, 
      status
    );

    res.json({
      success: true,
      count: enrollments.length,
      data: enrollments
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy lớp hiện tại của học sinh
// @route   GET /api/enrollment/student/:studentId/current
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getStudentCurrentClass = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { schoolYear } = req.query;

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.getStudentCurrentClass(
      studentId, 
      schoolYear
    );

    if (!enrollment) {
      return res.status(404).json({
        success: false,
        msg: 'Không tìm thấy lớp hiện tại của học sinh'
      });
    }

    res.json({
      success: true,
      data: enrollment
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy lịch sử học tập của học sinh
// @route   GET /api/enrollment/student/:studentId/history
// @access  Private (admin, giáo viên, chính học sinh đó)
exports.getStudentHistory = asyncHandler(async (req, res) => {
  const { studentId } = req.params;

  // Kiểm tra quyền truy cập
  if (req.user.role === ROLES.STUDENT && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const history = await StudentEnrollmentService.getStudentHistory(studentId);

    res.json({
      success: true,
      count: history.length,
      data: history
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Cập nhật trạng thái ghi danh
// @route   PUT /api/enrollment/status
// @access  Private (admin)
exports.updateEnrollmentStatus = asyncHandler(async (req, res) => {
  const { studentId, schoolYear, newStatus, reason } = req.body;

  // Chỉ admin mới được cập nhật trạng thái
  if (req.user.role !== ROLES.ADMIN) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.updateEnrollmentStatus(
      studentId, 
      schoolYear, 
      newStatus, 
      reason
    );

    res.json({
      success: true,
      data: enrollment,
      msg: 'Cập nhật trạng thái thành công'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy thống kê ghi danh theo lớp
// @route   GET /api/enrollment/stats/class/:classId
// @access  Private (admin, giáo viên)
exports.getClassEnrollmentStats = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear } = req.query;

  // Kiểm tra quyền truy cập
  if (req.user.role !== ROLES.ADMIN && req.user.role !== ROLES.TEACHER) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const stats = await StudentEnrollmentService.getClassEnrollmentStats(
      classId, 
      schoolYear
    );

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy số lượng học sinh trong lớp
// @route   GET /api/enrollment/count/class/:classId
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getStudentCount = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear, status = 'active' } = req.query;

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const count = await StudentEnrollmentService.getStudentCount(
      classId, 
      schoolYear, 
      status
    );

    res.json({
      success: true,
      data: { count }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});
