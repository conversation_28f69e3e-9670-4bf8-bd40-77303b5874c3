const mongoose = require('mongoose');

const announcementReadSchema = new mongoose.Schema({
  // Người dùng đã đọc thông báo
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },

  // Thông báo được đọc
  announcement: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Announcement',
    required: true,
  },

  // Trạng thái đã đọc
  isRead: {
    type: Boolean,
    default: true,
    required: true,
  },

  // Thời gian đọc
  readAt: {
    type: Date,
    default: Date.now,
    required: true,
  },

  // Thời gian tạo record
  createdAt: {
    type: Date,
    default: Date.now,
  },

  // Thời gian cập nhật
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Middleware để cập nhật updatedAt
announcementReadSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Index để tối ưu truy vấn
announcementReadSchema.index({ user: 1, announcement: 1 }, { unique: true }); // Mỗi user chỉ có 1 record cho mỗi announcement
announcementReadSchema.index({ announcement: 1, isRead: 1 });
announcementReadSchema.index({ user: 1, isRead: 1 });
announcementReadSchema.index({ readAt: -1 });

// Static method để đánh dấu đã đọc
announcementReadSchema.statics.markAsRead = async function(userId, announcementId) {
  return await this.findOneAndUpdate(
    {
      user: userId,
      announcement: announcementId
    },
    {
      user: userId,
      announcement: announcementId,
      isRead: true,
      readAt: new Date()
    },
    {
      upsert: true,
      new: true
    }
  );
};

// Static method để lấy thống kê đọc của một thông báo
announcementReadSchema.statics.getReadStats = async function(announcementId) {
  const stats = await this.aggregate([
    {
      $match: {
        announcement: new mongoose.Types.ObjectId(announcementId)
      }
    },
    {
      $group: {
        _id: '$isRead',
        count: { $sum: 1 }
      }
    }
  ]);

  const result = {
    totalRead: 0,
    totalUnread: 0
  };

  stats.forEach(stat => {
    if (stat._id === true) {
      result.totalRead = stat.count;
    } else {
      result.totalUnread = stat.count;
    }
  });

  return result;
};

// Static method để lấy danh sách thông báo chưa đọc của user
announcementReadSchema.statics.getUnreadAnnouncements = async function(userId, announcementIds) {
  const readRecords = await this.find({
    user: userId,
    announcement: { $in: announcementIds },
    isRead: true
  }).select('announcement');

  const readAnnouncementIds = readRecords.map(record => record.announcement.toString());
  return announcementIds.filter(id => !readAnnouncementIds.includes(id.toString()));
};

module.exports = mongoose.model('AnnouncementRead', announcementReadSchema);
