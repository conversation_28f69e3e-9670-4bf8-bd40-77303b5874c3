// scripts/testAttendanceAPI.js - Script để test API attendance statistics
const mongoose = require('mongoose');
require('dotenv').config();

async function testAttendanceAPI() {
  try {
    console.log('🚀 Bắt đầu test API attendance statistics...');
    
    // Kết nối database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Đã kết nối database');

    const db = mongoose.connection.db;
    
    // 1. Lấy danh sách tất cả các lớp
    console.log('\n📚 DANH SÁCH CÁC LỚP:');
    const classes = await db.collection('classes').find({}).toArray();
    
    classes.forEach(cls => {
      console.log(`🏫 ${cls.name} (${cls.schoolYear}) - ID: ${cls._id}`);
    });
    
    // 2. Test với lớp có ID từ URL lỗi
    const problemClassId = '6822eb7c592be2ab26a06b45';
    console.log(`\n🔍 KIỂM TRA LỚP CÓ VẤN ĐỀ: ${problemClassId}`);
    
    const problemClass = await db.collection('classes').findOne({
      _id: new mongoose.Types.ObjectId(problemClassId)
    });
    
    if (!problemClass) {
      console.log('❌ Không tìm thấy lớp với ID này');
    } else {
      console.log(`✅ Tìm thấy lớp: ${problemClass.name} (${problemClass.schoolYear})`);
      
      // Kiểm tra StudentEnrollment cho lớp này
      const enrollmentCount = await db.collection('studentenrollments').countDocuments({
        class: new mongoose.Types.ObjectId(problemClassId),
        schoolYear: problemClass.schoolYear,
        status: 'active'
      });
      
      console.log(`📋 Số học sinh enrollment: ${enrollmentCount}`);
      
      // Kiểm tra attendance records
      const attendanceCount = await db.collection('attendances').countDocuments({
        class: new mongoose.Types.ObjectId(problemClassId)
      });
      
      console.log(`✅ Số bản ghi điểm danh: ${attendanceCount}`);
      
      // Test StudentEnrollmentService
      try {
        const StudentEnrollmentService = require('../services/studentEnrollmentService');
        const serviceCount = await StudentEnrollmentService.getStudentCount(
          problemClassId, 
          problemClass.schoolYear
        );
        
        console.log(`🔧 StudentEnrollmentService trả về: ${serviceCount} học sinh`);
        
        // Test lấy danh sách học sinh
        const students = await StudentEnrollmentService.getClassStudents(
          problemClassId, 
          problemClass.schoolYear
        );
        
        console.log(`👥 Danh sách học sinh: ${students.length} người`);
        students.slice(0, 3).forEach(enrollment => {
          console.log(`   - ${enrollment.student.name} (${enrollment.student.studentId})`);
        });
        
      } catch (serviceError) {
        console.error('❌ Lỗi StudentEnrollmentService:', serviceError.message);
      }
    }
    
    // 3. Test với tất cả các lớp
    console.log('\n🧪 TEST TẤT CẢ CÁC LỚP:');
    
    for (const cls of classes) {
      try {
        const StudentEnrollmentService = require('../services/studentEnrollmentService');
        const studentCount = await StudentEnrollmentService.getStudentCount(
          cls._id.toString(), 
          cls.schoolYear
        );
        
        const attendanceCount = await db.collection('attendances').countDocuments({
          class: cls._id
        });
        
        console.log(`📊 ${cls.name}: ${studentCount} học sinh, ${attendanceCount} điểm danh`);
        
      } catch (error) {
        console.error(`❌ Lỗi với lớp ${cls.name}:`, error.message);
      }
    }
    
    // 4. Kiểm tra dữ liệu attendance trong khoảng thời gian cụ thể
    console.log('\n📅 KIỂM TRA ĐIỂM DANH NGÀY 2025-06-06:');
    
    const targetDate = new Date('2025-06-06');
    const startDate = new Date(targetDate);
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(targetDate);
    endDate.setHours(23, 59, 59, 999);
    
    const attendanceOnDate = await db.collection('attendances').find({
      date: { $gte: startDate, $lte: endDate }
    }).toArray();
    
    console.log(`📊 Tổng số điểm danh ngày 2025-06-06: ${attendanceOnDate.length}`);
    
    // Nhóm theo lớp
    const attendanceByClass = {};
    attendanceOnDate.forEach(att => {
      const classId = att.class.toString();
      if (!attendanceByClass[classId]) {
        attendanceByClass[classId] = [];
      }
      attendanceByClass[classId].push(att);
    });
    
    for (const [classId, attendances] of Object.entries(attendanceByClass)) {
      const cls = classes.find(c => c._id.toString() === classId);
      const className = cls ? cls.name : 'Unknown';
      console.log(`   - ${className}: ${attendances.length} bản ghi`);
    }
    
    // 5. Tạo dữ liệu test nếu cần
    if (attendanceOnDate.length === 0) {
      console.log('\n🔧 TẠO DỮ LIỆU TEST:');
      
      // Lấy một vài học sinh để tạo dữ liệu test
      const testStudents = await db.collection('studentenrollments').find({
        status: 'active'
      }).limit(5).toArray();
      
      for (const enrollment of testStudents) {
        const attendanceData = {
          student: enrollment.student,
          class: enrollment.class,
          date: new Date('2025-06-06T07:30:00.000Z'),
          session: 'morning',
          status: 'present',
          location: 'Trường THPT ABC',
          coordinates: {
            latitude: 10.762622,
            longitude: 106.660172
          },
          notes: 'Điểm danh test',
          createdBy: enrollment.student,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await db.collection('attendances').insertOne(attendanceData);
        console.log(`✅ Tạo điểm danh test cho học sinh trong lớp`);
      }
      
      console.log(`🎯 Đã tạo ${testStudents.length} bản ghi điểm danh test`);
    }
    
    console.log('\n✅ Test API attendance statistics hoàn thành!');
    
  } catch (error) {
    console.error('💥 Lỗi nghiêm trọng:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Đã ngắt kết nối database');
  }
}

// Chạy script
if (require.main === module) {
  testAttendanceAPI()
    .then(() => {
      console.log('✅ Script hoàn thành');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script thất bại:', error);
      process.exit(1);
    });
}

module.exports = testAttendanceAPI;
