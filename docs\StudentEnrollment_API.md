# Student Enrollment API Documentation

## Tổng quan

API StudentEnrollment được thiết kế để quản lý việc ghi danh học sinh vào lớp theo từng năm học, thay thế cho hệ thống cũ có vấn đề về data redundancy.

## Ưu điểm của hệ thống mới

### 🚀 Hiệu suất
- **Loại bỏ data redundancy**: Không còn lưu thông tin học sinh ở cả User.class và Class.students
- **Index tối ưu**: Query nhanh hơn với các index được thiết kế chuyên biệt
- **Query đơn giản**: Không cần sync dữ liệu giữa 2 collection

### 📊 Quản lý năm học
- **Lịch sử đầy đủ**: <PERSON> dõi được học sinh học lớp nào qua các năm
- **<PERSON><PERSON><PERSON><PERSON> lớp dễ dàng**: Hỗ trợ chuyển lớp trong cùng năm học
- **Trạng thái rõ ràng**: active, transferred, graduated, dropped

### 🔍 Truy vấn linh hoạt
- Lấy học sinh theo lớp + năm học
- Lấy lớp hiện tại của học sinh
- Thống kê theo nhiều tiêu chí

## Base URL
```
/api/enrollment
```

## Authentication
Tất cả endpoints đều yêu cầu authentication token trong header:
```
Authorization: Bearer <token>
```

## Endpoints

### 1. Ghi danh học sinh vào lớp
```http
POST /api/enrollment
```

**Body:**
```json
{
  "studentId": "ObjectId",
  "classId": "ObjectId", 
  "schoolYear": "2024-2025",
  "group": "A"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "ObjectId",
    "student": {
      "_id": "ObjectId",
      "name": "Nguyễn Văn A",
      "studentId": "HS001",
      "phoneNumber": "0123456789"
    },
    "class": {
      "_id": "ObjectId", 
      "name": "10A1",
      "group": "A",
      "schoolYear": "2024-2025"
    },
    "schoolYear": "2024-2025",
    "group": "A",
    "status": "active",
    "enrollmentDate": "2024-01-15T00:00:00.000Z"
  },
  "msg": "Ghi danh học sinh thành công"
}
```

### 2. Chuyển học sinh sang lớp khác
```http
PUT /api/enrollment/transfer
```

**Body:**
```json
{
  "studentId": "ObjectId",
  "newClassId": "ObjectId",
  "schoolYear": "2024-2025", 
  "reason": "Chuyển lớp theo yêu cầu phụ huynh"
}
```

### 3. Lấy danh sách học sinh của lớp
```http
GET /api/enrollment/class/:classId?schoolYear=2024-2025&status=active
```

**Response:**
```json
{
  "success": true,
  "count": 25,
  "data": [
    {
      "_id": "ObjectId",
      "student": {
        "_id": "ObjectId",
        "name": "Nguyễn Văn A",
        "studentId": "HS001",
        "phoneNumber": "0123456789",
        "avatar": "url",
        "gender": "male"
      },
      "schoolYear": "2024-2025",
      "group": "A",
      "status": "active",
      "enrollmentDate": "2024-01-15T00:00:00.000Z"
    }
  ]
}
```

### 4. Lấy lớp hiện tại của học sinh
```http
GET /api/enrollment/student/:studentId/current?schoolYear=2024-2025
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "ObjectId",
    "class": {
      "_id": "ObjectId",
      "name": "10A1", 
      "group": "A",
      "schoolYear": "2024-2025",
      "homeroomTeacher": "ObjectId"
    },
    "schoolYear": "2024-2025",
    "group": "A",
    "status": "active"
  }
}
```

### 5. Lấy lịch sử học tập của học sinh
```http
GET /api/enrollment/student/:studentId/history
```

**Response:**
```json
{
  "success": true,
  "count": 3,
  "data": [
    {
      "_id": "ObjectId",
      "class": {
        "_id": "ObjectId",
        "name": "10A1",
        "group": "A", 
        "schoolYear": "2024-2025"
      },
      "schoolYear": "2024-2025",
      "status": "active",
      "enrollmentDate": "2024-01-15T00:00:00.000Z"
    },
    {
      "_id": "ObjectId", 
      "class": {
        "_id": "ObjectId",
        "name": "9A2",
        "group": "A",
        "schoolYear": "2023-2024"
      },
      "schoolYear": "2023-2024", 
      "status": "graduated",
      "enrollmentDate": "2023-01-15T00:00:00.000Z",
      "endDate": "2024-06-30T00:00:00.000Z"
    }
  ]
}
```

### 6. Cập nhật trạng thái ghi danh
```http
PUT /api/enrollment/status
```

**Body:**
```json
{
  "studentId": "ObjectId",
  "schoolYear": "2024-2025",
  "newStatus": "graduated",
  "reason": "Hoàn thành chương trình lớp 12"
}
```

### 7. Thống kê ghi danh theo lớp
```http
GET /api/enrollment/stats/class/:classId?schoolYear=2024-2025
```

**Response:**
```json
{
  "success": true,
  "data": {
    "active": 25,
    "transferred": 2, 
    "graduated": 0,
    "dropped": 1,
    "total": 28
  }
}
```

### 8. Đếm số học sinh trong lớp
```http
GET /api/enrollment/count/class/:classId?schoolYear=2024-2025&status=active
```

**Response:**
```json
{
  "success": true,
  "data": {
    "count": 25
  }
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized  
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Enrollment Status

- `active` - Đang học
- `transferred` - Đã chuyển lớp
- `graduated` - Đã tốt nghiệp
- `dropped` - Đã thôi học

## Migration từ hệ thống cũ

Để chuyển đổi từ hệ thống cũ sang StudentEnrollment:

1. **Chạy migration script:**
```bash
node scripts/migrateToStudentEnrollment.js
```

2. **Kiểm tra tính nhất quán dữ liệu**
3. **Cập nhật frontend sử dụng API mới**
4. **Loại bỏ code cũ sau khi đã test kỹ**

## Performance Tips

1. **Sử dụng index hiệu quả:**
   - Query theo `{ class, schoolYear, status }`
   - Query theo `{ student, schoolYear }`

2. **Pagination cho danh sách lớn:**
```http
GET /api/enrollment/class/:classId?schoolYear=2024-2025&page=1&limit=20
```

3. **Cache kết quả đếm số học sinh** nếu cần thiết

## Error Handling

API trả về error message rõ ràng:

```json
{
  "success": false,
  "msg": "Học sinh đã có lớp trong năm học này"
}
```

## Security

- Giáo viên chỉ được truy cập lớp mình chủ nhiệm
- Học sinh chỉ xem được thông tin của chính mình
- Admin có quyền truy cập tất cả
