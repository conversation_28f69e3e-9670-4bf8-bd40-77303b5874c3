// scripts/fixDataMigration.js - <PERSON>ript để sửa dữ liệu và migration sang StudentEnrollment
const mongoose = require('mongoose');
require('dotenv').config();

async function fixDataMigration() {
  try {
    console.log('🚀 Bắt đầu sửa dữ liệu và migration...');
    
    // Kết nối database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Đã kết nối database');

    // Lấy dữ liệu trực tiếp từ MongoDB collections
    const db = mongoose.connection.db;
    
    // 1. L<PERSON>y tất cả users có role student và có class
    const studentsWithClass = await db.collection('users').find({
      role: 'student',
      class: { $exists: true, $ne: null }
    }).toArray();
    
    console.log(`📊 Tìm thấy ${studentsWithClass.length} học sinh có lớp`);

    // 2. <PERSON><PERSON><PERSON> tất cả classes
    const classes = await db.collection('classes').find({}).toArray();
    console.log(`📚 Tìm thấy ${classes.length} lớp học`);

    // 3. Tạo map classId -> classInfo để lookup nhanh
    const classMap = {};
    classes.forEach(cls => {
      classMap[cls._id.toString()] = cls;
    });

    // 4. Kiểm tra xem đã có StudentEnrollment collection chưa
    const existingEnrollments = await db.collection('studentenrollments').countDocuments();
    console.log(`📋 Hiện có ${existingEnrollments} enrollment records`);

    let successCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // 5. Tạo enrollment cho từng học sinh
    for (const student of studentsWithClass) {
      try {
        const classId = student.class.toString();
        const classInfo = classMap[classId];
        
        if (!classInfo) {
          console.log(`⚠️ Không tìm thấy thông tin lớp cho học sinh ${student.name}`);
          skippedCount++;
          continue;
        }

        // Kiểm tra xem đã có enrollment chưa
        const existingEnrollment = await db.collection('studentenrollments').findOne({
          student: student._id,
          class: student.class,
          schoolYear: classInfo.schoolYear
        });

        if (existingEnrollment) {
          console.log(`⚠️ Học sinh ${student.name} đã có enrollment, bỏ qua`);
          skippedCount++;
          continue;
        }

        // Tạo enrollment mới
        const enrollmentData = {
          student: student._id,
          class: student.class,
          schoolYear: classInfo.schoolYear,
          group: student.group || 'A',
          status: 'active',
          enrollmentDate: student.createdAt || new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        };

        await db.collection('studentenrollments').insertOne(enrollmentData);
        successCount++;
        console.log(`✅ Tạo enrollment cho ${student.name} - Lớp: ${classInfo.name}`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Lỗi với học sinh ${student.name}: ${error.message}`);
      }
    }

    // 6. Kiểm tra tính nhất quán
    console.log('\n🔍 KIỂM TRA TÍNH NHẤT QUÁN:');
    const totalStudents = await db.collection('users').countDocuments({ role: 'student' });
    const totalActiveEnrollments = await db.collection('studentenrollments').countDocuments({ status: 'active' });
    
    console.log(`👥 Tổng số học sinh: ${totalStudents}`);
    console.log(`📋 Tổng số enrollment active: ${totalActiveEnrollments}`);

    // 7. Thống kê theo lớp
    console.log('\n📊 THỐNG KÊ THEO LỚP:');
    for (const cls of classes) {
      const enrollmentCount = await db.collection('studentenrollments').countDocuments({
        class: cls._id,
        schoolYear: cls.schoolYear,
        status: 'active'
      });
      console.log(`📝 Lớp ${cls.name} (${cls.schoolYear}): ${enrollmentCount} học sinh`);
    }

    // 8. Báo cáo kết quả
    console.log('\n📈 KẾT QUẢ MIGRATION:');
    console.log(`✅ Thành công: ${successCount} enrollment`);
    console.log(`⚠️ Bỏ qua: ${skippedCount} enrollment`);
    console.log(`❌ Lỗi: ${errorCount} enrollment`);

    console.log('\n🎉 Migration hoàn thành!');
    console.log('\n📝 BƯỚC TIẾP THEO:');
    console.log('1. Test API attendance/statistics để xem có hoạt động không');
    console.log('2. Nếu OK, có thể tiếp tục sử dụng hệ thống mới');

  } catch (error) {
    console.error('💥 Lỗi nghiêm trọng:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Đã ngắt kết nối database');
  }
}

// Chạy migration
if (require.main === module) {
  fixDataMigration()
    .then(() => {
      console.log('✅ Migration script hoàn thành');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script thất bại:', error);
      process.exit(1);
    });
}

module.exports = fixDataMigration;
