// scripts/migrateToStudentEnrollment.js - Script migration từ hệ thống cũ sang StudentEnrollment
const mongoose = require('mongoose');
const fs = require('fs');
require('dotenv').config();

// Tạm thời định nghĩa schema cũ để migration
const oldUserSchema = new mongoose.Schema({
  name: String,
  role: String,
  class: { type: mongoose.Schema.Types.ObjectId, ref: 'Class' },
  group: String,
  createdAt: Date
}, { collection: 'users' });

const oldClassSchema = new mongoose.Schema({
  name: String,
  schoolYear: String,
  students: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
}, { collection: 'classes' });

// Models cho migration
const OldUser = mongoose.model('OldUser', oldUserSchema);
const OldClass = mongoose.model('OldClass', oldClassSchema);

// Import StudentEnrollment model
const StudentEnrollment = require('../models/StudentEnrollment');

async function migrateToStudentEnrollment() {
  try {
    console.log('🚀 Bắt đầu migration sang StudentEnrollment model...');

    // Kết nối MongoDB
    const uri = process.env.MONGO_URI || 'mongodb://localhost:27017/school_management';
    console.log('MONGO_URI:', uri);
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      family: 4
    });
    console.log('✅ Đã kết nối database');

    // Lấy học sinh có class
    const studentsWithClass = await OldUser.find({
      role: 'student',
      class: { $exists: true, $ne: null }
    }).lean(); // Sử dụng lean() để tăng hiệu suất
    console.log(`📊 Tìm thấy ${studentsWithClass.length} học sinh cần migration`);

    if (studentsWithClass.length === 0) {
      console.log('✅ Không có học sinh nào cần migration.');
      return;
    }

    let successCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const errors = [];

    // Tạo enrollment cho từng học sinh
    for (const student of studentsWithClass) {
      try {
        // Lấy thông tin lớp
        const classInfo = await OldClass.findById(student.class).lean();
        if (!classInfo) {
          skippedCount++;
          console.log(`⚠️ Không tìm thấy lớp cho học sinh ${student.name}, bỏ qua`);
          continue;
        }

        // Kiểm tra enrollment tồn tại
        const existingEnrollment = await StudentEnrollment.findOne({
          student: student._id,
          class: student.class,
          schoolYear: classInfo.schoolYear
        }).lean();

        if (existingEnrollment) {
          skippedCount++;
          console.log(`⚠️ Học sinh ${student.name} đã có enrollment, bỏ qua`);
          continue;
        }

        // Tạo enrollment mới
        const enrollment = new StudentEnrollment({
          student: student._id,
          class: student.class,
          schoolYear: classInfo.schoolYear,
          group: student.group || 'A',
          status: 'active',
          enrollmentDate: student.createdAt || new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        });

        await enrollment.save();
        successCount++;
        console.log(`✅ Đã tạo enrollment cho học sinh: ${student.name} - Lớp: ${classInfo.name}`);

      } catch (error) {
        errorCount++;
        const errorMsg = `❌ Lỗi khi tạo enrollment cho học sinh ${student.name}: ${error.message}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }

    // Kiểm tra và báo cáo Class model
    console.log('\n🔄 Kiểm tra Class model...');
    const classes = await OldClass.find({}).lean();
    for (const classObj of classes) {
      try {
        const enrollmentCount = await StudentEnrollment.countDocuments({
          class: classObj._id,
          schoolYear: classObj.schoolYear,
          status: 'active'
        });
        console.log(`📝 Lớp ${classObj.name}: ${enrollmentCount} học sinh`);
      } catch (error) {
        console.error(`❌ Lỗi khi kiểm tra lớp ${classObj.name}: ${error.message}`);
      }
    }

    // Tạo backup dữ liệu
    console.log('\n💾 Tạo backup dữ liệu cũ...');
    const classBackup = await OldClass.find({}).select('_id name students').lean();
    const userBackup = await OldUser.find({ role: 'student' }).select('_id name class group').lean();
    console.log(`📦 Backup ${classBackup.length} lớp và ${userBackup.length} học sinh`);

    const backupData = {
      timestamp: new Date().toISOString(),
      classes: classBackup,
      students: userBackup
    };

    const backupFileName = `backup_before_migration_${Date.now()}.json`;
    fs.writeFileSync(backupFileName, JSON.stringify(backupData, null, 2));
    console.log(`💾 Đã lưu backup vào ${backupFileName}`);

    // Báo cáo kết quả
    console.log('\n📈 KẾT QUẢ MIGRATION:');
    console.log(`✅ Thành công: ${successCount} enrollment`);
    console.log(`⚠️ Bỏ qua: ${skippedCount} enrollment`);
    console.log(`❌ Lỗi: ${errorCount} enrollment`);

    if (errors.length > 0) {
      console.log('\n🚨 CHI TIẾT LỖI:');
      errors.forEach(error => console.log(error));
    }

    // Kiểm tra tính nhất quán
    console.log('\n🔍 KIỂM TRA TÍNH NHẤT QUÁN:');
    const totalStudents = await OldUser.countDocuments({ role: 'student' });
    const totalEnrollments = await StudentEnrollment.countDocuments({ status: 'active' });
    console.log(`👥 Tổng số học sinh: ${totalStudents}`);
    console.log(`📋 Tổng số enrollment active: ${totalEnrollments}`);

    if (totalStudents === totalEnrollments) {
      console.log('✅ Dữ liệu nhất quán!');
    } else {
      console.log('⚠️ Có sự không nhất quán trong dữ liệu. Vui lòng kiểm tra.');
    }

    // Gợi ý bước tiếp theo
    console.log('\n📝 BƯỚC TIẾP THEO:');
    console.log('1. Kiểm tra ứng dụng hoạt động bình thường');
    console.log('2. Nếu OK, xóa trường class khỏi User model');
    console.log('3. Nếu OK, xóa trường students khỏi Class model');
    console.log('4. Cập nhật các controller sử dụng StudentEnrollment');

    console.log('\n🎉 Migration hoàn thành!');

  } catch (error) {
    console.error('💥 Lỗi nghiêm trọng trong quá trình migration:', error.message);
    throw error;
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Đã đóng kết nối database');
  }
}

// Chạy migration nếu file được gọi trực tiếp
if (require.main === module) {
  migrateToStudentEnrollment()
    .then(() => {
      console.log('✅ Migration script hoàn thành');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script thất bại:', error.message);
      process.exit(1);
    });
}

module.exports = migrateToStudentEnrollment;