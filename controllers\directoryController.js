// src/controllers/directoryController.js
const User = require('../models/User');
const Class = require('../models/Class');
const Directory = require('../models/Directory');
const Schedule = require('../models/Schedule');
const StudentEnrollment = require('../models/StudentEnrollment');
const StudentEnrollmentService = require('../services/studentEnrollmentService');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const { STUDENT_SPECIFIC_ROLES, TEACHER_SPECIFIC_ROLES } = require('../constants/userConstants');
const ROLES = require('../constants/roles');

// @desc    Lấy danh sách người dùng (có thể lọc theo role)
// @route   GET /api/directory
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUsers = asyncHandler(async (req, res) => {
    const { role, search, favorite, classId, subject } = req.query;

    let query = {};

    // Lọc theo vai trò
    if (role && Object.values(ROLES).includes(role)) {
        query.role = role;
    }

    // Tìm kiếm theo tên, lớp, bộ môn
    if (search) {
        query.$or = [
            { name: { $regex: search, $options: 'i' } },
            { studentId: { $regex: search, $options: 'i' } }
        ];

        // Nếu là giáo viên thì tìm theo môn học
        if (role === ROLES.TEACHER || !role) {
            query.$or.push({ department: { $regex: search, $options: 'i' } });
        }
    }

    // Lọc theo lớp
    if (classId) {
        query.class = classId;
    }

    // Lọc theo bộ môn
    if (subject) {
        query.department = subject;
    }

    // Lấy danh sách người dùng yêu thích
    if (favorite === 'true') {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory && userDirectory.favoriteContacts.length > 0) {
            query._id = { $in: userDirectory.favoriteContacts };
        } else {
            // Nếu không có người dùng yêu thích nào, trả về danh sách rỗng
            return res.json({
                success: true,
                count: 0,
                data: []
            });
        }
    }

    // Thực hiện truy vấn với reference
    const users = await User.find(query)
        .populate('class', 'name group')
        .select('-password')
        .sort({ name: 1 });

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu trả về
    const formattedUsers = users.map(user => {
        const contactInfo = user.getContactInfo();
        return {
            id: user._id,
            name: user.name,
            role: user.role,
            specificRole: user.specificRole,
            gender: user.gender,
            class: user.class ? user.class.name : null,
            group: user.role === 'student' ? (user.class ? user.class.group : user.group) : null,
            subject: user.department,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(user._id.toString())
        };
    });

    res.json({
        success: true,
        count: formattedUsers.length,
        data: formattedUsers
    });
});

// @desc    Lấy danh sách người dùng theo lớp
// @route   GET /api/directory/class/:classId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUsersByClass = asyncHandler(async (req, res) => {
    const { classId } = req.params;
    const { schoolYear } = req.query;

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Sử dụng schoolYear từ query hoặc từ class
    const targetYear = schoolYear || classObj.schoolYear;

    // Lấy danh sách học sinh trong lớp thông qua StudentEnrollment
    const enrollments = await StudentEnrollmentService.getClassStudents(classId, targetYear);
    const students = enrollments.map(enrollment => enrollment.student);

    // Lấy giáo viên chủ nhiệm
    const homeroomTeacher = await User.findById(classObj.homeroomTeacher)
        .select('-password');

    // Lấy các giáo viên bộ môn của lớp
    const subjectTeacherIds = classObj.subjectTeachers.map(st => st.teacher);
    const subjectTeachers = await User.find({ _id: { $in: subjectTeacherIds } })
        .select('-password');

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu học sinh
    const formattedStudents = students.map(student => {
        const contactInfo = student.getContactInfo();
        return {
            id: student._id,
            name: student.name,
            role: student.role,
            specificRole: student.specificRole,
            gender: student.gender,
            class: classObj.name,
            group: student.group || classObj.group,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(student._id.toString())
        };
    });

    // Định dạng dữ liệu giáo viên
    const formattedTeachers = [];

    if (homeroomTeacher) {
        const contactInfo = homeroomTeacher.getContactInfo();
        formattedTeachers.push({
            id: homeroomTeacher._id,
            name: homeroomTeacher.name,
            role: homeroomTeacher.role,
            specificRole: TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER,
            gender: homeroomTeacher.gender,
            subject: homeroomTeacher.department,
            displayRole: 'Giáo viên chủ nhiệm',
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(homeroomTeacher._id.toString())
        });
    }

    subjectTeachers.forEach(teacher => {
        if (teacher._id.toString() !== (homeroomTeacher ? homeroomTeacher._id.toString() : null)) {
            const contactInfo = teacher.getContactInfo();
            const subjectDetail = classObj.subjectTeachers.find(
                st => st.teacher.toString() === teacher._id.toString()
            );

            formattedTeachers.push({
                id: teacher._id,
                name: teacher.name,
                role: teacher.role,
                specificRole: teacher.specificRole,
                gender: teacher.gender,
                subject: subjectDetail ? subjectDetail.subject : teacher.department,
                displayRole: contactInfo.displayRole,
                displayMeta: contactInfo.displayMeta,
                favorite: favoriteContacts.includes(teacher._id.toString())
            });
        }
    });

    res.json({
        success: true,
        data: {
            classInfo: {
                id: classObj._id,
                name: classObj.name,
                group: classObj.group,
                schoolYear: classObj.schoolYear,
                classRoom: classObj.classRoom
            },
            students: formattedStudents,
            teachers: formattedTeachers
        }
    });
});

// @desc    Lấy danh sách giáo viên theo bộ môn
// @route   GET /api/directory/department/:subject
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getTeachersByDepartment = asyncHandler(async (req, res) => {
    const { subject } = req.params;

    // Lấy danh sách giáo viên theo bộ môn
    const teachers = await User.find({
        role: ROLES.TEACHER,
        department: subject
    })
        .select('-password')
        .sort({ specificRole: -1, name: 1 }); // Sắp xếp theo vai trò cụ thể (trưởng bộ môn lên đầu)

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu trả về
    const formattedTeachers = teachers.map(teacher => {
        const contactInfo = teacher.getContactInfo();
        return {
            id: teacher._id,
            name: teacher.name,
            role: teacher.role,
            specificRole: teacher.specificRole,
            gender: teacher.gender,
            subject: teacher.department,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(teacher._id.toString())
        };
    });

    // Lấy thông tin về bộ môn
    let departmentName = '';
    switch (subject) {
        case 'math': departmentName = 'Toán học'; break;
        case 'literature': departmentName = 'Ngữ văn'; break;
        case 'english': departmentName = 'Tiếng Anh'; break;
        case 'physics': departmentName = 'Vật lý'; break;
        case 'chemistry': departmentName = 'Hóa học'; break;
        case 'biology': departmentName = 'Sinh học'; break;
        case 'history': departmentName = 'Lịch sử'; break;
        case 'geography': departmentName = 'Địa lý'; break;
        default: departmentName = subject;
    }

    res.json({
        success: true,
        data: {
            department: {
                id: subject,
                name: departmentName
            },
            teachers: formattedTeachers
        }
    });
});

// @desc    Thêm/xóa người dùng khỏi danh sách yêu thích
// @route   POST /api/directory/favorite/:userId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.toggleFavorite = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    // Kiểm tra người dùng cần thêm vào yêu thích có tồn tại không
    const userToFavorite = await User.findById(userId);
    if (!userToFavorite) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Lấy hoặc tạo mới directory của người dùng
    let directory = await Directory.findOne({ user: req.user.id });
    if (!directory) {
        directory = new Directory({
            user: req.user.id,
            favoriteContacts: [],
            recentContacts: []
        });
    }

    // Kiểm tra xem đã yêu thích chưa
    const favoriteIndex = directory.favoriteContacts.indexOf(userId);
    let isFavorite;

    if (favoriteIndex === -1) {
        // Chưa yêu thích, thêm vào
        directory.favoriteContacts.push(userId);
        isFavorite = true;
    } else {
        // Đã yêu thích, xóa đi
        directory.favoriteContacts.splice(favoriteIndex, 1);
        isFavorite = false;
    }

    directory.updatedAt = Date.now();
    await directory.save();

    res.json({
        success: true,
        data: {
            isFavorite
        }
    });
});

// @desc    Lấy thông tin chi tiết của một người dùng
// @route   GET /api/directory/user/:userId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUserDetail = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    // Lấy thông tin người dùng (không populate class vì đã chuyển sang StudentEnrollment)
    const user = await User.findById(userId)
        .select('-password');

    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Kiểm tra xem người dùng có được yêu thích không
    let isFavorite = false;
    const directory = await Directory.findOne({ user: req.user.id });
    if (directory) {
        isFavorite = directory.favoriteContacts.includes(userId);
    }

    // Thêm vào danh sách liên hệ gần đây
    if (directory && userId !== req.user.id) {
        const recentIndex = directory.recentContacts.findIndex(
            contact => contact.contact.toString() === userId
        );

        if (recentIndex !== -1) {
            // Đã có trong danh sách gần đây, cập nhật thời gian
            directory.recentContacts[recentIndex].lastInteraction = Date.now();
        } else {
            // Chưa có trong danh sách gần đây, thêm vào
            directory.recentContacts.push({
                contact: userId,
                lastInteraction: Date.now()
            });
        }

        // Giới hạn danh sách liên hệ gần đây
        if (directory.recentContacts.length > 20) {
            // Sắp xếp theo thời gian tương tác gần nhất
            directory.recentContacts.sort((a, b) =>
                b.lastInteraction.getTime() - a.lastInteraction.getTime()
            );

            // Giữ lại 20 liên hệ gần nhất
            directory.recentContacts = directory.recentContacts.slice(0, 20);
        }

        directory.updatedAt = Date.now();
        await directory.save();
    }

    // Định dạng thông tin trả về
    const contactInfo = user.getContactInfo();

    // Lấy thêm thông tin lớp học nếu là giáo viên chủ nhiệm
    let homeroomClass = null;
    if (user.role === ROLES.TEACHER && user.specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER) {
        homeroomClass = await Class.findOne({ homeroomTeacher: userId })
            .select('name group schoolYear');
    }

    // Lấy thêm thông tin về các lớp dạy nếu là giáo viên bộ môn
    let teachingClasses = [];
    if (user.role === ROLES.TEACHER) {
        const classes = await Class.find({ 'subjectTeachers.teacher': userId })
            .select('name group schoolYear');

        teachingClasses = classes.map(c => ({
            id: c._id,
            name: c.name,
            group: c.group,
            schoolYear: c.schoolYear
        }));
    }

    const userDetail = {
        id: user._id,
        name: user.name,
        studentId: user.studentId || null,
        phoneNumber: user.phoneNumber,
        avatar: user.avatar,
        role: user.role,
        specificRole: user.specificRole,
        gender: user.gender,
        class: user.class ? {
            id: user.class._id,
            name: user.class.name,
            group: user.class.group,
            schoolYear: user.class.schoolYear
        } : null,
        group: user.group,
        department: user.department,
        displayRole: contactInfo.displayRole,
        displayMeta: contactInfo.displayMeta,
        favorite: isFavorite
    };

    if (homeroomClass) {
        userDetail.homeroomClass = {
            id: homeroomClass._id,
            name: homeroomClass.name,
            group: homeroomClass.group,
            schoolYear: homeroomClass.schoolYear
        };
    }

    if (teachingClasses.length > 0) {
        userDetail.teachingClasses = teachingClasses;
    }

    res.json({
        success: true,
        data: userDetail
    });
});

// @desc    Lấy danh sách lớp học
// @route   GET /api/directory/classes
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getClasses = asyncHandler(async (req, res) => {
    const { schoolYear } = req.query;

    let query = {};
    if (schoolYear) {
        query.schoolYear = schoolYear;
    }

    const classes = await Class.find(query)
        .populate('homeroomTeacher', 'name')
        .sort({ name: 1 });

    // Tính số học sinh cho từng lớp
    const formattedClasses = await Promise.all(classes.map(async (cls) => {
        const studentCount = await StudentEnrollmentService.getStudentCount(
            cls._id,
            schoolYear || cls.schoolYear
        );

        return {
            id: cls._id,
            name: cls.name,
            group: cls.group,
            schoolYear: cls.schoolYear,
            homeroomTeacher: cls.homeroomTeacher ? {
                id: cls.homeroomTeacher._id,
                name: cls.homeroomTeacher.name
            } : null,
            studentCount: studentCount,
            classRoom: cls.classRoom
        };
    }));

    res.json({
        success: true,
        count: formattedClasses.length,
        data: formattedClasses
    });
});

// @desc    Lấy danh sách bộ môn
// @route   GET /api/directory/departments
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getDepartments = asyncHandler(async (req, res) => {
    // Lấy tất cả các bộ môn từ giáo viên
    const departments = await User.distinct('department', { role: ROLES.TEACHER });

    // Tính số lượng giáo viên mỗi bộ môn
    const departmentCounts = {};
    for (const dept of departments) {
        departmentCounts[dept] = await User.countDocuments({
            role: ROLES.TEACHER,
            department: dept
        });
    }

    // Định dạng tên bộ môn
    const formattedDepartments = departments.map(dept => {
        let displayName = '';
        switch (dept) {
            case 'math': displayName = 'Toán học'; break;
            case 'literature': displayName = 'Ngữ văn'; break;
            case 'english': displayName = 'Tiếng Anh'; break;
            case 'physics': displayName = 'Vật lý'; break;
            case 'chemistry': displayName = 'Hóa học'; break;
            case 'biology': displayName = 'Sinh học'; break;
            case 'history': displayName = 'Lịch sử'; break;
            case 'geography': displayName = 'Địa lý'; break;
            default: displayName = dept;
        }

        return {
            id: dept,
            name: displayName,
            teacherCount: departmentCounts[dept] || 0
        };
    });

    // Sắp xếp bộ môn theo thứ tự ABC
    formattedDepartments.sort((a, b) => a.name.localeCompare(b.name));

    res.json({
        success: true,
        count: formattedDepartments.length,
        data: formattedDepartments
    });
});

// @desc    Tạo người dùng mới (học sinh hoặc giáo viên)
// @route   POST /api/directory/user
// @access  Private (admin)
exports.createUser = asyncHandler(async (req, res) => {
    const {
        name,
        studentId,
        password,
        phoneNumber,
        role,
        classId,
        gender,
        specificRole,
        teachingSubject,
        group
    } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name || !password || !phoneNumber || !gender) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra role hợp lệ
    if (!role || !['student', 'teacher', 'admin'].includes(role)) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra trường bắt buộc cho từng role
    if (role === ROLES.STUDENT && (!studentId || !classId)) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    if (role === ROLES.TEACHER && !teachingSubject) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra studentId có trùng không
    if (studentId) {
        const existingStudent = await User.findOne({ studentId });
        if (existingStudent) {
            return res.status(400).json({
                success: false,
                msg: MESSAGES.USER.ALREADY_EXISTS
            });
        }
    }

    // Kiểm tra số điện thoại có trùng không
    const existingPhone = await User.findOne({ phoneNumber });
    if (existingPhone) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.USER.ALREADY_EXISTS
        });
    }

    // Tạo người dùng mới
    const user = new User({
        name,
        studentId,
        password,
        phoneNumber,
        role,
        gender,
        specificRole: specificRole || (role === ROLES.STUDENT ? STUDENT_SPECIFIC_ROLES.REGULAR : TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER),
        teachingSubject
    });

    await user.save();

    // Nếu là học sinh, tạo ghi danh vào lớp
    if (role === ROLES.STUDENT && classId) {
        // Lấy thông tin lớp để biết năm học
        const classObj = await Class.findById(classId);
        if (classObj) {
            await StudentEnrollmentService.enrollStudent(
                user._id,
                classId,
                classObj.schoolYear,
                group || 'A'
            );
        }
    }

    // Nếu là giáo viên chủ nhiệm, cập nhật lớp học
    if (role === ROLES.TEACHER && specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER && classId) {
        await Class.findByIdAndUpdate(
            classId,
            { homeroomTeacher: user._id }
        );
    }

    res.status(201).json({
        success: true,
        data: {
            id: user._id,
            name: user.name,
            studentId: user.studentId,
            phoneNumber: user.phoneNumber,
            role: user.role,
            gender: user.gender,
            specificRole: user.specificRole
        }
    });
});

// @desc    Cập nhật thông tin người dùng
// @route   PUT /api/directory/user/:userId
// @access  Private (admin, giáo viên chỉ cập nhật được học sinh trong lớp của mình)
exports.updateUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const {
        name,
        phoneNumber,
        gender,
        specificRole,
        teachingSubject,
        group,
        classId
    } = req.body;

    // Lấy thông tin người dùng cần cập nhật
    const user = await User.findById(userId);
    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Kiểm tra quyền truy cập
    if (req.user.role === ROLES.TEACHER) {
        // Giáo viên chỉ cập nhật được học sinh trong lớp của mình
        if (user.role !== ROLES.STUDENT) {
            return res.status(403).json({
                success: false,
                msg: MESSAGES.ERROR.FORBIDDEN
            });
        }

        // Kiểm tra xem giáo viên có phải là chủ nhiệm của lớp học sinh không
        const studentClass = await Class.findById(user.class);
        if (!studentClass || studentClass.homeroomTeacher.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                msg: MESSAGES.ERROR.FORBIDDEN
            });
        }
    }

    // Cập nhật thông tin
    user.name = name || user.name;
    user.phoneNumber = phoneNumber || user.phoneNumber;
    user.gender = gender || user.gender;
    user.specificRole = specificRole || user.specificRole;

    if (user.role === 'teacher') {
        user.department = teachingSubject || user.department;
    }

    if (user.role === 'student') {
        user.group = group || user.group;

        // Xử lý chuyển lớp nếu có
        if (classId && classId !== user.class.toString()) {
            // Xóa khỏi lớp cũ
            await Class.findByIdAndUpdate(
                user.class,
                { $pull: { students: user._id } }
            );

            // Thêm vào lớp mới
            await Class.findByIdAndUpdate(
                classId,
                { $addToSet: { students: user._id } }
            );

            user.class = classId;
        }
    }

    await user.save();

    res.json({
        success: true,
        data: {
            id: user._id,
            name: user.name,
            phoneNumber: user.phoneNumber,
            role: user.role,
            gender: user.gender,
            specificRole: user.specificRole,
            class: user.class
        }
    });
});

// @desc    Xóa người dùng
// @route   DELETE /api/directory/user/:userId
// @access  Private (admin)
exports.deleteUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findById(userId);
    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Xử lý khi xóa học sinh
    if (user.role === ROLES.STUDENT && user.class) {
        // Xóa học sinh khỏi danh sách lớp
        await Class.findByIdAndUpdate(
            user.class,
            { $pull: { students: user._id } }
        );
    }

    // Xử lý khi xóa giáo viên
    if (user.role === ROLES.TEACHER) {
        // Kiểm tra xem có phải là giáo viên chủ nhiệm của lớp nào không
        const homeroomClass = await Class.findOne({ homeroomTeacher: userId });
        if (homeroomClass) {
            homeroomClass.homeroomTeacher = null;
            await homeroomClass.save();
        }

        // Xóa khỏi danh sách giáo viên bộ môn
        await Class.updateMany(
            { 'subjectTeachers.teacher': userId },
            { $pull: { subjectTeachers: { teacher: userId } } }
        );
    }

    // Xóa người dùng
    await User.findByIdAndDelete(userId);

    // Xóa khỏi danh sách yêu thích của tất cả người dùng
    await Directory.updateMany(
        { favoriteContacts: userId },
        { $pull: { favoriteContacts: userId } }
    );

    // Xóa khỏi danh sách liên hệ gần đây
    await Directory.updateMany(
        { 'recentContacts.contact': userId },
        { $pull: { recentContacts: { contact: userId } } }
    );

    res.json({
        success: true,
        data: {}
    });
});

// @desc    Tạo lớp học mới
// @route   POST /api/directory/class
// @access  Private (admin)
exports.createClass = asyncHandler(async (req, res) => {
    const {
        name,
        schoolYear,
        group,
        classRoom,
        homeroomTeacherId
    } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name || !schoolYear || !group) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra xem lớp đã tồn tại chưa
    const existingClass = await Class.findOne({ name });
    if (existingClass) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.CLASS.ALREADY_EXISTS
        });
    }

    // Nếu có homeroomTeacherId, kiểm tra giáo viên có tồn tại không
    if (homeroomTeacherId) {
        const teacher = await User.findById(homeroomTeacherId);
        if (!teacher || teacher.role !== 'teacher') {
            return res.status(404).json({
                success: false,
                msg: MESSAGES.USER.NOT_FOUND
            });
        }
    }

    // Tạo lớp mới
    const newClass = new Class({
        name,
        schoolYear,
        group,
        classRoom: classRoom || 'Phòng học chung',
        homeroomTeacher: homeroomTeacherId || null,
        students: [],
        subjectTeachers: []
    });

    await newClass.save();

    // Nếu có giáo viên chủ nhiệm, cập nhật specificRole
    if (homeroomTeacherId) {
        await User.findByIdAndUpdate(
            homeroomTeacherId,
            { specificRole: TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER }
        );
    }

    res.status(201).json({
        success: true,
        data: newClass
    });
});

// @desc    Cập nhật thông tin lớp học
// @route   PUT /api/directory/class/:classId
// @access  Private (admin, giáo viên chủ nhiệm)
exports.updateClass = asyncHandler(async (req, res) => {
    const { classId } = req.params;
    const {
        name,
        schoolYear,
        group,
        classRoom,
        homeroomTeacherId
    } = req.body;

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra quyền truy cập
    if (req.user.role === 'teacher') {
        // Giáo viên phải là chủ nhiệm của lớp
        if (classObj.homeroomTeacher.toString() !== req.user.id) {
            return res.status(403).json({
                success: false,
                msg: MESSAGES.ERROR.FORBIDDEN
            });
        }

        // Giáo viên chỉ được phép cập nhật một số thông tin
        if (name || schoolYear || homeroomTeacherId) {
            return res.status(403).json({
                success: false,
                msg: MESSAGES.ERROR.FORBIDDEN
            });
        }
    }

    // Xử lý thay đổi giáo viên chủ nhiệm
    if (homeroomTeacherId && homeroomTeacherId !== classObj.homeroomTeacher?.toString()) {
        // Kiểm tra giáo viên mới
        const newTeacher = await User.findById(homeroomTeacherId);
        if (!newTeacher || newTeacher.role !== 'teacher') {
            return res.status(404).json({
                success: false,
                msg: MESSAGES.USER.NOT_FOUND
            });
        }

        // Cập nhật giáo viên cũ (nếu có)
        if (classObj.homeroomTeacher) {
            const oldTeacher = await User.findById(classObj.homeroomTeacher);
            if (oldTeacher && oldTeacher.specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER) {
                oldTeacher.specificRole = TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER;
                await oldTeacher.save();
            }
        }

        // Cập nhật giáo viên mới
        newTeacher.specificRole = TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER;
        await newTeacher.save();

        classObj.homeroomTeacher = homeroomTeacherId;
    }

    // Cập nhật thông tin khác
    if (name) classObj.name = name;
    if (schoolYear) classObj.schoolYear = schoolYear;
    if (group) classObj.group = group;
    if (classRoom) classObj.classRoom = classRoom;

    await classObj.save();

    res.json({
        success: true,
        data: classObj
    });
});

// @desc    Xóa lớp học
// @route   DELETE /api/directory/class/:classId
// @access  Private (admin)
exports.deleteClass = asyncHandler(async (req, res) => {
    const { classId } = req.params;

    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra xem lớp có học sinh không
    if (classObj.students.length > 0) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Xóa tham chiếu đến lớp học trong giáo viên chủ nhiệm
    if (classObj.homeroomTeacher) {
        const teacher = await User.findById(classObj.homeroomTeacher);
        if (teacher && teacher.specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER) {
            teacher.specificRole = TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER;
            await teacher.save();
        }
    }

    await Class.findByIdAndDelete(classId);

    res.json({
        success: true,
        data: {}
    });
});

// @desc    Thêm học sinh vào lớp
// @route   POST /api/directory/class/:classId/students
// @access  Private (admin, giáo viên chủ nhiệm)
exports.addStudentsToClass = asyncHandler(async (req, res) => {
    const { classId } = req.params;
    const { studentIds } = req.body;

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra quyền truy cập
    if (req.user.role === 'teacher' && classObj.homeroomTeacher.toString() !== req.user.id) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Kiểm tra các học sinh có tồn tại không
    const students = await User.find({
        _id: { $in: studentIds },
        role: 'student'
    });

    if (students.length !== studentIds.length) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Thêm học sinh vào lớp
    for (const student of students) {
        // Xóa khỏi lớp cũ nếu có
        if (student.class) {
            await Class.findByIdAndUpdate(
                student.class,
                { $pull: { students: student._id } }
            );
        }

        // Cập nhật lớp mới cho học sinh
        student.class = classId;
        await student.save();
    }

    // Cập nhật danh sách học sinh trong lớp
    classObj.students = [...new Set([...classObj.students, ...studentIds])];
    await classObj.save();

    res.json({
        success: true,
        data: {
            addedStudents: students.length,
            classInfo: {
                id: classObj._id,
                name: classObj.name,
                studentCount: classObj.students.length
            }
        }
    });
});

// @desc    Xóa học sinh khỏi lớp
// @route   DELETE /api/directory/class/:classId/students/:studentId
// @access  Private (admin, giáo viên chủ nhiệm)
exports.removeStudentFromClass = asyncHandler(async (req, res) => {
    const { classId, studentId } = req.params;

    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra quyền truy cập
    if (req.user.role === 'teacher' && classObj.homeroomTeacher.toString() !== req.user.id) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Kiểm tra học sinh có trong lớp không
    if (!classObj.students.includes(studentId)) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Xóa học sinh khỏi lớp
    classObj.students = classObj.students.filter(id => id.toString() !== studentId);
    await classObj.save();

    // Cập nhật thông tin học sinh
    await User.findByIdAndUpdate(
        studentId,
        { $unset: { class: 1 } }
    );

    res.json({
        success: true,
        data: {
            classInfo: {
                id: classObj._id,
                name: classObj.name,
                studentCount: classObj.students.length
            }
        }
    });
});

// @desc    Lấy các lớp mà giáo viên dạy
// @route   GET /api/directory/teacher/classes
// @access  Private (giáo viên)
exports.getTeacherClasses = asyncHandler(async (req, res) => {
    const teacherId = req.user.id;

    // Kiểm tra quyền truy cập
    if (req.user.role !== ROLES.TEACHER) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Tìm các lớp mà giáo viên dạy (bao gồm cả chủ nhiệm và dạy môn)
    const classes = await Class.find({
        $or: [
            { homeroomTeacher: teacherId },
            { 'subjectTeachers.teacher': teacherId }
        ]
    })
    .populate('homeroomTeacher', 'name')
    .populate('subjectTeachers.teacher', 'name')
    .sort({ name: 1 });

    // Lấy thời khóa biểu cho tất cả các lớp mà giáo viên dạy
    const classIds = classes.map(cls => cls._id);
    const schedules = await Schedule.find({
        class: { $in: classIds },
        'periods.teacher': teacherId
    })
    .populate('class', 'name')
    .sort({ day: 1, 'periods.periodNumber': 1 });

    const formattedClasses = classes.map(cls => {
        // Tìm các môn học mà giáo viên này dạy trong lớp
        const teachingSubjects = cls.subjectTeachers
            .filter(st => st.teacher && st.teacher._id && st.teacher._id.toString() === teacherId)
            .map(st => st.subject);

        // Kiểm tra xem có phải giáo viên chủ nhiệm không
        const isHomeroomTeacher = cls.homeroomTeacher &&
            cls.homeroomTeacher._id.toString() === teacherId;

        // Lấy các tiết dạy của giáo viên trong lớp này
        const classSchedules = schedules.filter(schedule =>
            schedule.class._id.toString() === cls._id.toString()
        );

        const teachingPeriods = [];
        classSchedules.forEach(schedule => {
            schedule.periods.forEach(period => {
                if (period.teacher && period.teacher.toString() === teacherId) {
                    teachingPeriods.push({
                        day: schedule.day,
                        periodNumber: period.periodNumber,
                        subject: period.subject,
                        room: period.room || cls.classRoom,
                        schoolYear: schedule.schoolYear
                    });
                }
            });
        });

        // Sắp xếp các tiết dạy theo ngày và tiết
        teachingPeriods.sort((a, b) => {
            const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            const dayComparison = dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day);
            if (dayComparison !== 0) return dayComparison;
            return a.periodNumber - b.periodNumber;
        });

        return {
            id: cls._id,
            name: cls.name,
            group: cls.group,
            schoolYear: cls.schoolYear,
            classRoom: cls.classRoom,
            studentCount: cls.students.length,
            role: isHomeroomTeacher ? 'homeroom' : 'subject',
            teachingSubjects: teachingSubjects,
            teachingPeriods: teachingPeriods,
            homeroomTeacher: cls.homeroomTeacher ? {
                id: cls.homeroomTeacher._id,
                name: cls.homeroomTeacher.name
            } : null
        };
    });

    res.json({
        success: true,
        count: formattedClasses.length,
        data: formattedClasses
    });
});

// @desc    Lấy thông tin chi tiết lớp học với đầy đủ giáo viên
// @route   GET /api/directory/class/:classId/details
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getClassDetails = asyncHandler(async (req, res) => {
    const { classId } = req.params;

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId)
        .populate('homeroomTeacher', 'name phoneNumber')
        .populate('subjectTeachers.teacher', 'name phoneNumber')
        .populate('students', 'name studentId');

    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Format dữ liệu trả về
    const formattedClass = {
        id: classObj._id,
        name: classObj.name,
        group: classObj.group,
        schoolYear: classObj.schoolYear,
        classRoom: classObj.classRoom,
        createdAt: classObj.createdAt,
        homeroomTeacher: classObj.homeroomTeacher ? {
            id: classObj.homeroomTeacher._id,
            name: classObj.homeroomTeacher.name,
            phoneNumber: classObj.homeroomTeacher.phoneNumber
        } : null,
        subjectTeachers: classObj.subjectTeachers
            .filter(st => st.teacher) // Lọc bỏ những teacher null
            .map(st => ({
                subject: st.subject,
                teacher: {
                    id: st.teacher._id,
                    name: st.teacher.name,
                    phoneNumber: st.teacher.phoneNumber
                }
            })),
        students: classObj.students.map(student => ({
            id: student._id,
            name: student.name,
            studentId: student.studentId
        })),
        studentCount: classObj.students.length
    };

    res.json({
        success: true,
        data: formattedClass
    });
});

module.exports = {
    getUsers: exports.getUsers,
    getUsersByClass: exports.getUsersByClass,
    getTeachersByDepartment: exports.getTeachersByDepartment,
    toggleFavorite: exports.toggleFavorite,
    getUserDetail: exports.getUserDetail,
    getClasses: exports.getClasses,
    getDepartments: exports.getDepartments,
    createUser: exports.createUser,
    updateUser: exports.updateUser,
    deleteUser: exports.deleteUser,
    createClass: exports.createClass,
    updateClass: exports.updateClass,
    deleteClass: exports.deleteClass,
    addStudentsToClass: exports.addStudentsToClass,
    removeStudentFromClass: exports.removeStudentFromClass,
    getTeacherClasses: exports.getTeacherClasses,
    getClassDetails: exports.getClassDetails
};