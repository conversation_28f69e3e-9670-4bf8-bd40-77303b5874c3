// utils/schoolYear.js - Utility functions để tính toán năm học
const getCurrentSchoolYear = () => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1; // getMonth() trả về 0-11
  
  // Năm học thường bắt đầu từ tháng 9
  // Nếu hiện tại là tháng 1-8, thì đang ở năm học trước
  // Nếu hiện tại là tháng 9-12, thì đang ở năm học mới
  
  if (currentMonth >= 9) {
    // Tháng 9-12: năm học mới bắt đầu
    return `${currentYear}-${currentYear + 1}`;
  } else {
    // Tháng 1-8: vẫn đang ở năm học trước
    return `${currentYear - 1}-${currentYear}`;
  }
};

const getSchoolYearFromDate = (date) => {
  const targetDate = new Date(date);
  const year = targetDate.getFullYear();
  const month = targetDate.getMonth() + 1;
  
  if (month >= 9) {
    return `${year}-${year + 1}`;
  } else {
    return `${year - 1}-${year}`;
  }
};

const parseSchoolYear = (schoolYear) => {
  const [startYear, endYear] = schoolYear.split('-').map(Number);
  return {
    startYear,
    endYear,
    startDate: new Date(startYear, 8, 1), // 1 tháng 9
    endDate: new Date(endYear, 5, 30)     // 30 tháng 6
  };
};

const isValidSchoolYear = (schoolYear) => {
  const regex = /^\d{4}-\d{4}$/;
  if (!regex.test(schoolYear)) {
    return false;
  }
  
  const [startYear, endYear] = schoolYear.split('-').map(Number);
  return endYear === startYear + 1;
};

const getAllSchoolYears = (fromYear = 2020, toYear = null) => {
  const currentYear = new Date().getFullYear();
  const endYear = toYear || currentYear + 2;
  
  const years = [];
  for (let year = fromYear; year <= endYear; year++) {
    years.push(`${year}-${year + 1}`);
  }
  
  return years;
};

const getNextSchoolYear = (schoolYear) => {
  const [startYear] = schoolYear.split('-').map(Number);
  return `${startYear + 1}-${startYear + 2}`;
};

const getPreviousSchoolYear = (schoolYear) => {
  const [startYear] = schoolYear.split('-').map(Number);
  return `${startYear - 1}-${startYear}`;
};

module.exports = {
  getCurrentSchoolYear,
  getSchoolYearFromDate,
  parseSchoolYear,
  isValidSchoolYear,
  getAllSchoolYears,
  getNextSchoolYear,
  getPreviousSchoolYear
};
