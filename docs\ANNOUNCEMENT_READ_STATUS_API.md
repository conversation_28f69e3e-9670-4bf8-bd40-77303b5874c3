# API Documentation: Announcement Read Status

## Tổng quan

Hệ thống thông báo đã được cập nhật để hỗ trợ tính năng đánh dấu đã đọc/chưa đọc và hiển thị chi tiết nội dung thông báo.

## C<PERSON><PERSON> thay đổi chính

### 1. Model Updates

#### Announcement Model
- **Thêm field `title`**: Tiêu đề thông báo (bắt buộc, tối đa 200 ký tự)
- **Virtual fields**: `readCount`, `readBy` để đếm và lấy danh sách người đã đọc
- **Methods**: `isReadByUser()`, `markAsReadByUser()` để kiểm tra và đánh dấu đã đọc

#### AnnouncementRead Model (Mới)
- **user**: ID người dùng đã đọc
- **announcement**: ID thông báo đư<PERSON><PERSON> đọc
- **isRead**: Trạng thái đã đ<PERSON> (boolean)
- **readAt**: <PERSON>h<PERSON><PERSON> gian đ<PERSON>
- **Static methods**: `markAsRead()`, `getReadStats()`, `getUnreadAnnouncements()`

### 2. API Endpoints

#### Tạo thông báo mới
```http
POST /api/announcements
```

**Request Body:**
```json
{
  "title": "Tiêu đề thông báo",
  "type": "teacher_to_student",
  "content": "Nội dung thông báo",
  "recipients": {
    "class": "classId",
    "subject": "Toán học"
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "zaloGroupId"
  }
}
```

**Lưu ý về Status tự động:**
- Nếu `zaloConfig.enabled = false` hoặc không có `zaloConfig`: Status tự động = `"sent"`
- Nếu `zaloConfig.enabled = true`: Status mặc định = `"draft"` (có thể set thành `"scheduled"`)

**Ví dụ thông báo không dùng Zalo (tự động sent):**
```json
{
  "title": "Thông báo nội bộ",
  "type": "teacher_to_student",
  "content": "Thông báo chỉ hiển thị trong app",
  "recipients": {
    "class": "classId"
  },
  "zaloConfig": {
    "enabled": false
  }
  // Status sẽ tự động là "sent"
}
```

#### Lấy danh sách thông báo (có trạng thái đọc)
```http
GET /api/announcements?page=1&limit=10
```

**Response:**
```json
{
  "announcements": [
    {
      "_id": "announcementId",
      "title": "Tiêu đề thông báo",
      "content": "Nội dung thông báo",
      "type": "teacher_to_student",
      "sender": {
        "name": "Tên giáo viên",
        "role": "teacher"
      },
      "createdAt": "2024-01-15T08:00:00Z",
      "isRead": false
    }
  ],
  "currentPage": 1,
  "totalPages": 5,
  "totalAnnouncements": 50
}
```

#### Lấy thông báo mới nhất (có trạng thái đọc)
```http
GET /api/announcements/recent?limit=5
```

#### Lấy chi tiết thông báo (tự động đánh dấu đã đọc)
```http
GET /api/announcements/:id
```

**Response:**
```json
{
  "_id": "announcementId",
  "title": "Tiêu đề thông báo",
  "content": "Nội dung chi tiết thông báo",
  "type": "teacher_to_student",
  "sender": {
    "name": "Tên giáo viên",
    "role": "teacher"
  },
  "recipients": {
    "class": {
      "name": "Lớp 10A1"
    },
    "subject": "Toán học"
  },
  "createdAt": "2024-01-15T08:00:00Z",
  "isRead": true
}
```

#### Đánh dấu thông báo đã đọc
```http
POST /api/announcements/:id/read
```

**Response:**
```json
{
  "success": true,
  "message": "Đánh dấu đã đọc thành công"
}
```

#### Lấy thống kê đọc thông báo (chỉ người tạo/admin)
```http
GET /api/announcements/:id/read-status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "announcement": {
      "_id": "announcementId",
      "title": "Tiêu đề thông báo",
      "type": "teacher_to_student"
    },
    "stats": {
      "totalRead": 25,
      "totalUnread": 5
    },
    "readBy": [
      {
        "user": {
          "name": "Nguyễn Văn A",
          "role": "student"
        },
        "readAt": "2024-01-15T09:30:00Z"
      }
    ]
  }
}
```

#### Lấy danh sách thông báo chưa đọc
```http
GET /api/announcements/unread?limit=10
```

**Response:**
```json
{
  "success": true,
  "data": {
    "unreadCount": 3,
    "announcements": [
      {
        "_id": "announcementId",
        "title": "Thông báo mới",
        "type": "teacher_to_student",
        "createdAt": "2024-01-15T08:00:00Z"
      }
    ]
  }
}
```

### 3. Cập nhật thông báo
```http
PUT /api/announcements/:id
```

**Request Body:**
```json
{
  "title": "Tiêu đề mới",
  "content": "Nội dung mới",
  "recipients": {
    "class": "newClassId"
  }
}
```

## Quy trình sử dụng

### Cho Frontend
1. **Hiển thị danh sách**: Gọi `/api/announcements` để lấy danh sách với trạng thái đọc
2. **Hiển thị badge chưa đọc**: Sử dụng field `isRead` để hiển thị indicator
3. **Xem chi tiết**: Gọi `/api/announcements/:id` - tự động đánh dấu đã đọc
4. **Đếm chưa đọc**: Gọi `/api/announcements/unread` để lấy số lượng chưa đọc
5. **Thống kê (admin/teacher)**: Gọi `/api/announcements/:id/read-status` để xem ai đã đọc

### Cho Mobile App
- Tương tự như frontend
- Có thể cache trạng thái đọc locally
- Sync với server khi có kết nối

## Lưu ý quan trọng

1. **Tự động đánh dấu đã đọc**: Khi gọi `GET /api/announcements/:id`, hệ thống tự động đánh dấu đã đọc
2. **Quyền xem thống kê**: Chỉ người tạo thông báo hoặc admin mới xem được thống kê đọc
3. **Performance**: Sử dụng index để tối ưu truy vấn read status
4. **Validation**: Title bắt buộc và không quá 200 ký tự
5. **Auto Status Logic**:
   - `zaloConfig.enabled = false` → Status tự động = `"sent"`
   - `zaloConfig.enabled = true` → Status mặc định = `"draft"`
   - Thông báo không dùng Zalo sẽ hiển thị ngay lập tức cho users

## Migration Notes

- Các thông báo cũ sẽ cần thêm title (có thể dùng substring của content)
- AnnouncementRead collection sẽ được tạo mới
- Không ảnh hưởng đến dữ liệu hiện có
