# Announcement System Update

## Overview
The announcement system has been completely redesigned to support multiple types of announcements with different sender-recipient relationships, Zalo integration, and advanced scheduling capabilities.

## Changes Made

### 1. Updated Announcement Model (`models/Announcement.js`)
- **New Structure**: Complete redesign with flexible recipient system
- **Announcement Types**: Support for 4 different types:
  - `teacher_to_student`: Gi<PERSON>o viên gửi học sinh
  - `principal_to_teacher`: Hi<PERSON>u trưởng gửi giáo viên  
  - `head_to_teacher`: Trưởng bộ môn gửi giáo viên
  - `admin_to_all`: Quản trị viên gửi toàn trường

- **Flexible Recipients**: Support for multiple recipient types:
  - Class-based (for student announcements)
  - Teacher-specific lists
  - Department-based
  - School-wide announcements

- **Zalo Integration**: Built-in Zalo configuration for automated messaging
- **Status Management**: Draft, scheduled, and sent statuses
- **Optimized Indexes**: For better query performance

### 2. New Constants File (`constants/announcementConstants.js`)
- Announcement types and status constants
- Display labels in Vietnamese
- Common department names
- Centralized configuration

### 3. Completely Rewritten Controller (`controllers/announcementController.js`)
- **Permission-based Access**: Different permissions for different announcement types
- **Smart Query Building**: Automatically filters announcements based on user role
- **Validation**: Comprehensive validation for recipients based on announcement type
- **New Endpoints**:
  - `createAnnouncement`: Create with new flexible structure
  - `getAnnouncements`: Get with filtering and pagination
  - `getRecentAnnouncements`: Get recent announcements
  - `getAnnouncementById`: Get specific announcement details
  - `updateAnnouncement`: Update with permission checks
  - `deleteAnnouncement`: Delete with permission checks
  - `getAnnouncementConfig`: Get available types, statuses, departments
  - `getAnnouncementStats`: Get announcement statistics

### 4. Updated Routes (`routes/announcement.js`)
- Added new endpoints for CRUD operations
- Added configuration and statistics endpoints
- Removed role-specific authorization (now handled in controller logic)

### 5. Enhanced Messages (`constants/messages.js`)
- Added comprehensive announcement-related messages
- Error messages for permissions and validation

## API Usage Examples

### Creating Announcements

#### Teacher to Student Announcement
```javascript
POST /api/announcements
{
  "type": "teacher_to_student",
  "content": "Thông báo về bài kiểm tra tuần tới",
  "recipients": {
    "class": "classId",
    "subject": "Toán học"
  },
  "zaloConfig": {
    "enabled": true,
    "groupId": "zaloGroupId",
    "scheduledTime": "2024-01-15T08:00:00Z"
  }
}
```

#### Principal to Teachers Announcement
```javascript
POST /api/announcements
{
  "type": "principal_to_teacher",
  "content": "Thông báo họp giáo viên",
  "recipients": {
    "department": "Toán",
    // or "teachers": ["teacherId1", "teacherId2"]
    // or "schoolWide": true
  }
}
```

### Querying Announcements

#### Get Announcements with Filters
```javascript
GET /api/announcements?page=1&limit=10&type=teacher_to_student&classId=classId&status=sent
```

#### Get Recent Announcements
```javascript
GET /api/announcements/recent?limit=5&classId=classId
```

#### Get Configuration
```javascript
GET /api/announcements/config
// Returns: { types, statuses, departments }
```

#### Get Statistics
```javascript
GET /api/announcements/stats?startDate=2024-01-01&endDate=2024-01-31
// Returns: { total, byType, byStatus }
```

## Permission System

### Teacher to Student (`teacher_to_student`)
- **Who can create**: Teachers, Admins
- **Who can view**: Students in the specified class, the sender, Admins

### Principal to Teacher (`principal_to_teacher`)
- **Who can create**: Admins (assuming principal role is admin)
- **Who can view**: Specified teachers, department members, or all teachers if school-wide

### Head to Teacher (`head_to_teacher`)
- **Who can create**: Department heads, Admins
- **Who can view**: Specified teachers, department members, or all teachers if school-wide

### Admin to All (`admin_to_all`)
- **Who can create**: Admins only
- **Who can view**: Everyone in the school

## Migration Notes

### Breaking Changes
- **Model Structure**: Complete change from simple teacher-class-subject to flexible sender-recipients
- **API Endpoints**: New request/response formats
- **Permission Logic**: Now handled in controller rather than middleware

### Backward Compatibility
- Old announcement data will need migration
- Frontend applications will need updates to use new API structure

## Zalo Integration Features

- **Group Messaging**: Send announcements to Zalo groups
- **Scheduling**: Schedule announcements for future delivery
- **Status Tracking**: Track if Zalo messages were sent successfully
- **Configuration**: Per-announcement Zalo settings

## Database Indexes

The following indexes are automatically created for optimal performance:
- `{ type: 1, createdAt: -1 }`
- `{ 'recipients.class': 1, type: 1 }`
- `{ 'recipients.department': 1 }`
- `{ 'zaloConfig.scheduledTime': 1, status: 1 }`

## Next Steps

1. **Test the new endpoints** with different user roles
2. **Update frontend applications** to use the new API structure
3. **Implement Zalo integration** service for automated messaging
4. **Create migration script** for existing announcement data
5. **Add unit tests** for the new functionality
