/**
 * T<PERSON><PERSON> hợp các thông báo được sử dụng trong ứng dụng
 * Gi<PERSON><PERSON> thống nhất nội dung và dễ dàng thay đổi/đa ngôn ngữ sau này
 */
const MESSAGES = {
  // Thông báo lỗi chung
  ERROR: {
    GENERAL: 'Đã xảy ra lỗi, vui lòng thử lại sau',
    UNAUTHORIZED: 'Không có quyền truy cập',
    FORBIDDEN: 'Bạn không có quyền thực hiện hành động này',
    NOT_FOUND: 'Không tìm thấy tài nguyên yêu cầu',
    VALIDATION_FAILED: 'Dữ liệu không hợp lệ',
    MISSING_INFO: 'Vui lòng cung cấp đầy đủ thông tin',
    INVALID_DATA: 'Dữ liệu không hợp lệ',
    OPERATION_FAILED: 'Thao tác thất bại'
  },

  // Thông báo liên quan đến người dùng
  USER: {
    NOT_FOUND: 'Không tìm thấy người dùng',
    ALREADY_EXISTS: 'Người dùng đã tồn tại',
    INVALID_CREDENTIALS: 'Thông tin đăng nhập không chính xác',
    CREATED: 'Tạo người dùng thành công',
    UPDATED: 'Cập nhật thông tin người dùng thành công',
    DELETED: 'Xóa người dùng thành công',
    STUDENT_MUST_HAVE_CLASS: 'Học sinh phải có lớp học',
    INVALID_CLASS_ID: 'ID lớp học không hợp lệ',
    STUDENT_MUST_HAVE_STUDENT_ID: 'Học sinh phải có mã số học sinh',
    TEACHER_MUST_HAVE_SUBJECT: 'Giáo viên phải có ít nhất một môn học',
    SOME_SUBJECTS_NOT_EXIST: 'Một hoặc nhiều môn học không tồn tại',
    MISSING_CLASS: 'Học sinh phải có lớp học',
    MISSING_STUDENT_ID: 'Học sinh phải có mã số học sinh',
    MISSING_TEACHING_SUBJECTS: 'Giáo viên phải có ít nhất một môn học',
    INVALID_TEACHING_SUBJECTS: 'Một hoặc nhiều môn học không tồn tại',
    PHONE_ALREADY_EXISTS: 'Số điện thoại đã được sử dụng'
  },

  // Thông báo liên quan đến học sinh
  STUDENT: {
    NOT_FOUND: 'Không tìm thấy học sinh',
    ALREADY_EXISTS: 'Học sinh đã tồn tại',
    CREATED: 'Tạo học sinh thành công',
    UPDATED: 'Cập nhật thông tin học sinh thành công',
    DELETED: 'Xóa học sinh thành công',
  },

  // Thông báo liên quan đến lớp học
  CLASS: {
    NOT_FOUND: 'Không tìm thấy lớp học',
    ALREADY_EXISTS: 'Lớp học đã tồn tại',
    CREATED: 'Tạo lớp học thành công',
    UPDATED: 'Cập nhật thông tin lớp học thành công',
    DELETED: 'Xóa lớp học thành công',
    SOME_NOT_EXIST: 'Một số lớp không tồn tại',
    ASSIGN_LIST_REQUIRED: 'Vui lòng cung cấp danh sách lớp',
  },

  // Thông báo liên quan đến điểm số
  GRADE: {
    NOT_FOUND: 'Không tìm thấy điểm số',
    CREATED: 'Tạo điểm số thành công',
    UPDATED: 'Cập nhật điểm số thành công',
    DELETED: 'Xóa điểm số thành công',
  },

  // Thông báo liên quan đến môn học
  SUBJECT: {
    NOT_FOUND: 'Không tìm thấy môn học',
    ALREADY_EXISTS: 'Môn học đã tồn tại',
    CREATED: 'Tạo môn học thành công',
    UPDATED: 'Cập nhật thông tin môn học thành công',
    DELETED: 'Xóa môn học thành công',
  },

  // Thông báo liên quan đến tin tức
  NEWS: {
    NOT_FOUND: 'Không tìm thấy tin tức',
    CREATED: 'Tạo tin tức thành công',
    UPDATED: 'Cập nhật tin tức thành công',
    DELETED: 'Xóa tin tức thành công',
  },

  // Thông báo liên quan đến sự kiện
  EVENT: {
    NOT_FOUND: 'Không tìm thấy sự kiện',
    CREATED: 'Tạo sự kiện thành công',
    UPDATED: 'Cập nhật sự kiện thành công',
    DELETED: 'Xóa sự kiện thành công',
  },

  // Thông báo liên quan đến lịch học
  SCHEDULE: {
    NOT_FOUND: 'Không tìm thấy lịch học',
    CREATED: 'Tạo lịch học thành công',
    UPDATED: 'Cập nhật lịch học thành công',
    DELETED: 'Xóa lịch học thành công',
  },

  // Thông báo liên quan đến điểm danh
  ATTENDANCE: {
    NOT_FOUND: 'Không tìm thấy thông tin điểm danh',
    CREATED: 'Điểm danh thành công',
    UPDATED: 'Cập nhật điểm danh thành công',
    DELETED: 'Xóa điểm danh thành công',
    CONFIG_NOT_FOUND: 'Không tìm thấy cấu hình điểm danh',
    INVALID_TIME: 'Thời gian điểm danh không hợp lệ',
    STUDENT_ALREADY_ATTENDED: 'Học sinh đã được điểm danh',
  },

  // Thông báo liên quan đến cấu hình điểm danh
  ATTENDANCE_CONFIG: {
    NOT_FOUND: 'Không tìm thấy cấu hình điểm danh',
    CREATED: 'Tạo cấu hình điểm danh thành công',
    UPDATED: 'Cập nhật cấu hình điểm danh thành công',
    DELETED: 'Xóa cấu hình điểm danh thành công',
  },

  // Thông báo liên quan đến đơn xin nghỉ
  LEAVE_REQUEST: {
    NOT_FOUND: 'Không tìm thấy đơn xin nghỉ',
    CREATED: 'Tạo đơn xin nghỉ thành công',
    UPDATED: 'Cập nhật đơn xin nghỉ thành công',
    DELETED: 'Xóa đơn xin nghỉ thành công',
    APPROVED: 'Đơn xin nghỉ đã được chấp nhận',
    REJECTED: 'Đơn xin nghỉ đã bị từ chối',
    INVALID_DATE_RANGE: 'Khoảng thời gian không hợp lệ',
    ALREADY_EXISTS_IN_PERIOD: 'Đã có đơn xin nghỉ trong khoảng thời gian này',
    CANNOT_APPROVE_OWN_REQUEST: 'Không thể duyệt đơn của chính mình',
  },

  // Thông báo liên quan đến đề thi và bài thi
  EXAM: {
    NOT_FOUND: 'Không tìm thấy đề thi',
    CREATED: 'Tạo đề thi thành công',
    UPDATED: 'Cập nhật đề thi thành công',
    DELETED: 'Đề thi đã được xóa',
    NO_PERMISSION_UPDATE: 'Bạn không có quyền cập nhật đề thi này',
    NO_PERMISSION_DELETE: 'Bạn không có quyền xóa đề thi này',
    NO_PERMISSION_ADD_QUESTION: 'Bạn không có quyền thêm câu hỏi vào đề thi này',
    NO_PERMISSION_VIEW_QUESTIONS: 'Bạn không có quyền xem tất cả câu hỏi',
    NO_PERMISSION_ASSIGN: 'Bạn không có quyền phân công đề thi này',
    NO_PERMISSION_UNASSIGN: 'Bạn không có quyền hủy phân công đề thi này',
    NO_PERMISSION_VIEW_RESULTS: 'Bạn không có quyền xem kết quả này',
    NO_PERMISSION_SUBMIT: 'Bạn không có quyền nộp bài thi này',
    NO_PERMISSION_ANSWER: 'Bạn không có quyền trả lời câu hỏi này',
    NO_PERMISSION_VIEW_INFO: 'Bạn không có quyền xem thông tin này',
    NOT_ACTIVE_OR_EXPIRED: 'Đề thi chưa được mở hoặc đã hết hạn',
    NO_QUESTIONS: 'Đề thi chưa có câu hỏi',
    ASSIGNED_CLASSES: 'Phân công đề thi cho các lớp thành công',
    UNASSIGNED_CLASS: 'Hủy phân công lớp khỏi đề thi thành công',
  },

  // Thông báo liên quan đến câu hỏi
  QUESTION: {
    NOT_FOUND: 'Không tìm thấy câu hỏi',
    CREATED: 'Thêm câu hỏi thành công',
    UPDATED: 'Cập nhật câu hỏi thành công',
    DELETED: 'Câu hỏi đã được xóa',
    NO_PERMISSION_UPDATE: 'Bạn không có quyền cập nhật câu hỏi này',
    NO_PERMISSION_DELETE: 'Bạn không có quyền xóa câu hỏi này',
    NOT_IN_ATTEMPT: 'Không tìm thấy câu hỏi trong lần làm bài',
  },

  // Thông báo liên quan đến lần làm bài
  EXAM_ATTEMPT: {
    NOT_FOUND: 'Không tìm thấy lần làm bài',
    CREATED: 'Bắt đầu làm bài thi thành công',
    SUBMITTED: 'Nộp bài thi thành công',
    ALREADY_COMPLETED: 'Lần làm bài đã hoàn thành',
    ANSWER_SAVED: 'Lưu câu trả lời thành công',
  },

  // Thông báo liên quan đến thông báo
  ANNOUNCEMENT: {
    NOT_FOUND: 'Không tìm thấy thông báo',
    CREATED: 'Tạo thông báo thành công',
    UPDATED: 'Cập nhật thông báo thành công',
    DELETED: 'Xóa thông báo thành công',
    SENT: 'Gửi thông báo thành công',
    CANNOT_EDIT_SENT: 'Không thể sửa thông báo đã gửi',
    INVALID_TYPE: 'Loại thông báo không hợp lệ',
    INVALID_RECIPIENTS: 'Đối tượng nhận thông báo không hợp lệ',
    NO_PERMISSION_CREATE: 'Bạn không có quyền tạo loại thông báo này',
    NO_PERMISSION_VIEW: 'Bạn không có quyền xem thông báo này',
    NO_PERMISSION_EDIT: 'Bạn không có quyền sửa thông báo này',
    NO_PERMISSION_DELETE: 'Bạn không có quyền xóa thông báo này',
    ZALO_NOT_CONFIGURED: 'Thông báo chưa được cấu hình Zalo',
    ZALO_ALREADY_SENT: 'Thông báo đã được gửi qua Zalo',
    MARKED_AS_READ: 'Đánh dấu đã đọc thành công',
    ALREADY_READ: 'Thông báo đã được đánh dấu là đã đọc',
    READ_STATUS_RETRIEVED: 'Lấy trạng thái đọc thành công',
    TITLE_REQUIRED: 'Tiêu đề thông báo là bắt buộc',
    TITLE_TOO_LONG: 'Tiêu đề thông báo không được vượt quá 200 ký tự',
  },

  // Thông báo liên quan đến Zalo
  ZALO: {
    USER_NOT_FOUND: 'Không tìm thấy user',
    GROUP_NOT_FOUND: 'Không tìm thấy nhóm trong danh sách của user',
    SEND_SUCCESS: 'Gửi tin nhắn Zalo thành công',
    SEND_FAILED: 'Gửi tin nhắn Zalo thất bại',
    INVALID_GROUP_ID: 'ID nhóm Zalo không hợp lệ',
  },

  // Thông báo liên quan đến directory/danh bạ
  DIRECTORY: {
    SEARCH_TERM_REQUIRED: 'Vui lòng nhập từ khóa tìm kiếm',
    CONTACT_ADDED: 'Thêm liên hệ yêu thích thành công',
    CONTACT_REMOVED: 'Xóa liên hệ yêu thích thành công',
    FAVORITE_NOT_FOUND: 'Không tìm thấy liên hệ yêu thích',
  }
};

module.exports = MESSAGES;