// src/models/Class.js
const mongoose = require('mongoose');

const ClassSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true, // Ví dụ: "10A1"
    unique: true,
    trim: true,
  },
  schoolYear: {
    type: String,
    required: true, // Ví dụ: "2024-2025"
    trim: true,
  },
  homeroomTeacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Tham chiếu giáo viên chủ nhiệm
    required: false,
  },
  // Trường students đã được chuyển sang StudentEnrollment model để tránh data redundancy
  // và hỗ trợ quản lý học sinh theo năm học tốt hơn
  createdAt: {
    type: Date,
    default: Date.now,
  },
  // Thêm trường mới
  group: {
    type: String, // Ví dụ: "A", "B", "C"
    required: true,
  },
  subjectTeachers: [{
    teacher: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    subject: {
      type: String,
      required: true
    }
  }],
  classRoom: {
    type: String, // Ví dụ: "Phòng học A"
    default: "Phòng học chung"
  }
});

// Virtual field để tính số học sinh hiện tại
ClassSchema.virtual('studentCount').get(function() {
  // Sẽ được populate từ StudentEnrollment trong controller
  return this._studentCount || 0;
});

// Method để lấy số học sinh theo năm học
ClassSchema.methods.getStudentCount = async function(schoolYear = null) {
  const StudentEnrollment = require('./StudentEnrollment');
  const targetYear = schoolYear || this.schoolYear;

  return await StudentEnrollment.getStudentCount(this._id, targetYear);
};

// Method để lấy danh sách học sinh theo năm học
ClassSchema.methods.getStudents = async function(schoolYear = null) {
  const StudentEnrollment = require('./StudentEnrollment');
  const targetYear = schoolYear || this.schoolYear;

  return await StudentEnrollment.getCurrentStudents(this._id, targetYear);
};

// Đảm bảo virtual fields được include khi convert to JSON
ClassSchema.set('toJSON', { virtuals: true });
ClassSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Class', ClassSchema);